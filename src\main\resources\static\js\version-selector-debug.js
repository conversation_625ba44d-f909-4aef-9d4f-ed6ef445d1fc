/**
 * 版本选择器调试和测试脚本
 * 用于验证下拉菜单和滚动问题的修复
 *
 * 设置 window.disableVersionSelectorDebug = true 来禁用调试
 */

$(document).ready(function() {
    // 检查是否禁用调试
    if (window.disableVersionSelectorDebug === true) {
        console.log('🔧 版本选择器调试已禁用');
        return;
    }

    console.log('🔧 版本选择器调试脚本已加载');

    // 检查当前页面是否需要版本选择器功能
    const needsVersionSelector = checkIfVersionSelectorNeeded();

    if (needsVersionSelector) {
        console.log('📄 检测到需要版本选择器的页面，启动调试测试...');
        // 延迟执行调试检查，确保页面完全加载
        setTimeout(function() {
            runVersionSelectorTests();
        }, 2000);
    } else {
        console.log('📄 当前页面不需要版本选择器，跳过调试测试');
    }
});

/**
 * 检查当前页面是否需要版本选择器功能
 */
function checkIfVersionSelectorNeeded() {
    // 检查页面URL或特定元素来判断是否需要版本选择器
    const currentPath = window.location.pathname;

    // 需要版本选择器的页面路径
    const versionSelectorPages = [
        '/papers',
        '/generate',
        '/history',
        '/paper-history'
    ];

    // 检查URL是否匹配
    const urlMatches = versionSelectorPages.some(page => currentPath.includes(page));

    // 检查是否存在相关的DOM元素
    const hasRelevantElements = $('.paper-history, .paper-list, #paperHistoryTable, .download-btn').length > 0;

    // 检查是否存在试卷相关的模态框
    const hasPaperModals = $('#paperGenerationModal, #paperHistoryModal').length > 0;

    return urlMatches || hasRelevantElements || hasPaperModals;
}

/**
 * 运行版本选择器测试
 */
function runVersionSelectorTests() {
    console.log('🧪 开始版本选择器测试...');

    // 测试1: 检查版本选择器是否存在
    testVersionSelectorExists();

    // 测试2: 检查事件绑定
    testEventBindings();

    // 测试3: 检查CSS样式
    testCSSStyles();

    // 测试4: 检查滚动功能
    testScrollFunctionality();

    // 测试5: 模拟下拉菜单交互
    testDropdownInteraction();

    console.log('✅ 版本选择器测试完成');
}

/**
 * 测试版本选择器是否存在
 */
function testVersionSelectorExists() {
    const selectors = $('.version-selector');
    const downloadButtons = $('.download-with-version');
    const enhancedMenus = $('.enhanced-download-menu');
    const paperElements = $('.paper-history, .paper-list, #paperHistoryTable');

    console.log(`📊 测试结果:`);
    console.log(`- 版本选择器数量: ${selectors.length}`);
    console.log(`- 下载按钮数量: ${downloadButtons.length}`);
    console.log(`- 增强菜单数量: ${enhancedMenus.length}`);
    console.log(`- 试卷相关元素数量: ${paperElements.length}`);

    if (selectors.length === 0) {
        if (paperElements.length > 0) {
            console.info('ℹ️ 找到试卷相关元素但未找到版本选择器，可能页面还未完全加载或试卷历史为空');
        } else {
            console.info('ℹ️ 当前页面不包含版本选择器相关功能');
        }
    } else {
        console.log('✅ 版本选择器已正确加载');
    }
}

/**
 * 测试事件绑定
 */
function testEventBindings() {
    console.log('🔗 测试事件绑定...');

    // 检查是否有事件监听器
    const hasChangeEvent = $._data(document, 'events') &&
                          $._data(document, 'events').change &&
                          $._data(document, 'events').change.some(e => e.selector === '.version-selector');

    const hasClickEvent = $._data(document, 'events') &&
                         $._data(document, 'events').click &&
                         $._data(document, 'events').click.some(e => e.selector === '.download-with-version');

    console.log(`- 版本选择器change事件: ${hasChangeEvent ? '✅' : '❌'}`);
    console.log(`- 下载按钮click事件: ${hasClickEvent ? '✅' : '❌'}`);
}

/**
 * 测试CSS样式
 */
function testCSSStyles() {
    console.log('🎨 测试CSS样式...');

    // 检查body滚动样式
    const bodyOverflow = $('body').css('overflow-y');
    console.log(`- Body overflow-y: ${bodyOverflow}`);

    // 检查容器样式
    const containerPadding = $('.container-fluid').css('padding-bottom');
    console.log(`- Container padding-bottom: ${containerPadding}`);

    // 检查增强菜单样式
    const menuMinWidth = $('.enhanced-download-menu').css('min-width');
    console.log(`- 增强菜单最小宽度: ${menuMinWidth}`);

    // 检查右侧历史试卷区域
    const rightSidebarHeight = $('.col-lg-3 .card').css('max-height');
    console.log(`- 右侧历史试卷最大高度: ${rightSidebarHeight}`);
}

/**
 * 测试滚动功能
 */
function testScrollFunctionality() {
    console.log('📜 测试滚动功能...');

    // 检查页面高度
    const documentHeight = $(document).height();
    const windowHeight = $(window).height();
    const canScroll = documentHeight > windowHeight;

    console.log(`- 文档高度: ${documentHeight}px`);
    console.log(`- 窗口高度: ${windowHeight}px`);
    console.log(`- 可以滚动: ${canScroll ? '✅' : '❌'}`);

    // 测试滚动到底部
    if (canScroll) {
        console.log('🔄 测试滚动到底部...');
        const originalScrollTop = $(window).scrollTop();

        // 滚动到底部
        $('html, body').animate({
            scrollTop: documentHeight
        }, 500, function() {
            const newScrollTop = $(window).scrollTop();
            console.log(`- 滚动前位置: ${originalScrollTop}px`);
            console.log(`- 滚动后位置: ${newScrollTop}px`);
            console.log(`- 滚动成功: ${newScrollTop > originalScrollTop ? '✅' : '❌'}`);

            // 滚动回顶部
            $('html, body').animate({
                scrollTop: 0
            }, 500);
        });
    }
}

/**
 * 测试下拉菜单交互
 */
function testDropdownInteraction() {
    console.log('🖱️ 测试下拉菜单交互...');

    // 查找第一个下拉菜单
    const firstDropdown = $('.dropdown-toggle').first();

    if (firstDropdown.length > 0) {
        console.log('📋 找到下拉菜单，测试交互...');

        // 模拟点击打开下拉菜单
        firstDropdown.trigger('click');

        setTimeout(function() {
            const isOpen = firstDropdown.next('.dropdown-menu').hasClass('show');
            console.log(`- 下拉菜单打开: ${isOpen ? '✅' : '❌'}`);

            if (isOpen) {
                // 测试版本选择器点击
                const versionSelector = firstDropdown.next('.dropdown-menu').find('.version-selector').first();
                if (versionSelector.length > 0) {
                    console.log('🔧 测试版本选择器点击...');

                    // 模拟点击版本选择器
                    versionSelector.trigger('click');

                    setTimeout(function() {
                        const stillOpen = firstDropdown.next('.dropdown-menu').hasClass('show');
                        console.log(`- 点击版本选择器后菜单仍打开: ${stillOpen ? '✅' : '❌'}`);

                        // 关闭下拉菜单
                        firstDropdown.trigger('click');
                    }, 100);
                }
            }
        }, 100);
    } else {
        console.log('❌ 未找到下拉菜单');
    }
}

/**
 * 手动测试函数 - 可在控制台调用
 */
window.testVersionSelector = function() {
    console.log('🚀 手动测试版本选择器...');
    runVersionSelectorTests();
};

/**
 * 修复下拉菜单问题的紧急修复函数
 */
window.fixDropdownIssues = function() {
    console.log('🔧 应用紧急修复...');

    // 重新绑定事件，确保阻止冒泡
    $(document).off('click.dropdown-fix').on('click.dropdown-fix', '.version-selector, .enhanced-download-menu', function(e) {
        e.stopPropagation();
        console.log('🛑 阻止事件冒泡');
    });

    // 确保页面可以滚动
    $('body').css({
        'overflow-y': 'auto',
        'overflow-x': 'hidden'
    });

    $('.container-fluid').css({
        'min-height': '100vh',
        'padding-bottom': '50px'
    });

    console.log('✅ 紧急修复已应用');
};

/**
 * 显示调试信息面板
 */
window.showDebugPanel = function() {
    const debugInfo = `
        <div style="font-family: monospace; font-size: 12px; text-align: left;">
            <h6>🔧 版本选择器调试信息</h6>
            <p><strong>版本选择器:</strong> ${$('.version-selector').length} 个</p>
            <p><strong>下载按钮:</strong> ${$('.download-with-version').length} 个</p>
            <p><strong>增强菜单:</strong> ${$('.enhanced-download-menu').length} 个</p>
            <p><strong>页面高度:</strong> ${$(document).height()}px</p>
            <p><strong>可滚动:</strong> ${$(document).height() > $(window).height() ? '是' : '否'}</p>
            <hr>
            <p><strong>快速修复:</strong></p>
            <button onclick="window.fixDropdownIssues()" class="btn btn-sm btn-warning">应用修复</button>
            <button onclick="window.testVersionSelector()" class="btn btn-sm btn-info ml-2">重新测试</button>
        </div>
    `;

    Swal.fire({
        title: '调试面板',
        html: debugInfo,
        width: '500px',
        showConfirmButton: false,
        showCloseButton: true
    });
};

// 在控制台提供快捷命令提示
console.log(`
🔧 版本选择器调试命令:
- testVersionSelector() - 运行完整测试
- fixDropdownIssues() - 应用紧急修复
- showDebugPanel() - 显示调试面板
- window.disableVersionSelectorDebug = true - 禁用自动调试
`);
