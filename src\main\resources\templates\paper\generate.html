<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试卷生成 - 麦子教育系统</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/paper-generate.css">
    <link rel="stylesheet" href="/static/css/paper-real-time-preview.css">
    <link rel="stylesheet" href="/static/css/paper-history.css">
    <!-- 知识点搜索功能样式 -->
    <link rel="stylesheet" href="/static/css/knowledge-search.css">
    <!--  模态框滚动修复样式 -->
    <link rel="stylesheet" href="/static/css/modal-scroll-fix.css">
    <!-- 🧠 智能组卷流程介绍样式 -->
    <link rel="stylesheet" href="/static/css/algorithm-flow.css">
    <!--  下拉菜单修复样式 -->
    <link rel="stylesheet" href="/static/css/dropdown-menu-fix.css">

    <!-- Add Markdown-it for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@12.0.6/dist/markdown-it.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

    <!-- 引入官方推荐的 Polyfill -->
    <script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"></script>

    <!-- KaTeX Support -->
    <div th:replace="fragments/katex-support :: katex"></div>
</head>
<body>
    <div class="container-fluid mt-3">
        <header class="page-header mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-file-alt"></i> 试卷生成</h2>
                    <p class="text-muted">选择知识点，配置题型和难度，生成个性化试卷</p>
                </div>
                <div class="d-flex gap-2">
                    <!--  配置管理按钮 -->
                    <button class="btn btn-outline-primary" id="loadConfigBtn" title="加载已保存的配置">
                        <i class="fas fa-folder-open me-1"></i>
                        <span class="d-none d-md-inline">加载配置</span>
                    </button>
                    <button class="btn btn-outline-success" id="saveConfigBtn" title="保存当前配置" disabled>
                        <i class="fas fa-save me-1"></i>
                        <span class="d-none d-md-inline">保存配置</span>
                    </button>
                    <a href="/paper-configs" class="btn btn-outline-secondary" title="配置管理">
                        <i class="fas fa-cog me-1"></i>
                        <span class="d-none d-md-inline">配置管理</span>
                    </a>
                </div>
            </div>
        </header>

        <div class="row">
            <!-- 左侧知识点选择区域 -->
            <div class="col-lg-3 col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-brain mr-2"></i>知识点选择</h5>
                            <!-- 搜索栏 -->
                            <div class="search-container" style="width: 280px;">
                                <div class="input-group input-group-sm">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text bg-white border-0">
                                            <i class="fas fa-search text-muted"></i>
                                        </span>
                                    </div>
                                    <input type="text" class="form-control border-0" id="knowledgePointSearchMain"
                                           placeholder="输入知识点名称搜索..." autocomplete="off">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-light btn-sm" type="button" id="searchBtnMain"
                                                title="搜索">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        <button class="btn btn-outline-light btn-sm" type="button" id="clearSearchMain"
                                                style="display: none;" title="清空搜索">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 搜索状态栏 -->
                        <div class="search-status-bar mt-2" id="searchStatusBar" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="search-info">
                                    <small class="text-light">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        <span id="searchStatusText">搜索结果</span>
                                    </small>
                                </div>
                                <div class="search-actions">
                                    <button class="btn btn-outline-light btn-xs" id="backToCategories" style="display: none;">
                                        <i class="fas fa-arrow-left mr-1"></i>返回分类
                                    </button>
                                    <button class="btn btn-outline-light btn-xs ml-1" id="testSearchBtn" onclick="testSearch()" title="测试搜索">
                                        <i class="fas fa-flask"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="knowledge-points-container">
                            <div class="row no-gutters">
                                <!-- 知识点分类列表 -->
                                <div class="col-md-4 border-right">
                                    <div class="knowledge-categories p-2">
                                        <div class="list-group list-group-flush" id="knowledge-groups">
                                            <div class="text-center py-5 text-muted">
                                                <div class="spinner-border" role="status">
                                                    <span class="sr-only">加载中...</span>
                                                </div>
                                                <p class="mt-2">加载知识点分类...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 知识点列表 -->
                                <div class="col-md-8">
                                    <div id="knowledge-points-container" class="p-2">
                                        <div class="text-center py-5 text-muted" id="defaultKnowledgeMessage">
                                            <i class="fas fa-hand-point-left fa-3x mb-3"></i>
                                            <p>请先选择一个知识点分类</p>
                                        </div>
                                        <!-- 搜索无结果提示 -->
                                        <div class="text-center py-5 text-muted" id="noSearchResults" style="display: none;">
                                            <i class="fas fa-search fa-3x mb-3"></i>
                                            <p>未找到匹配的知识点</p>
                                            <small>请尝试其他关键词或清空搜索条件</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge badge-primary selectedCount">0</span> 个知识点已选择
                            </div>
                            <button class="btn btn-primary" id="generatePaperBtn" disabled>
                                <i class="fas fa-magic mr-1"></i>生成试卷
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 左侧已选择知识点区域（移动设备上显示） -->
                <div class="card mb-4 d-md-none" id="left-selected-knowledge-points-card" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-check-circle mr-2"></i>已选择的知识点</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info" id="left-no-selected-points-message">
                            <i class="fas fa-info-circle mr-2"></i>尚未选择任何知识点，请从上方选择知识点
                        </div>
                        <div id="left-selected-knowledge-points-container" class="row">
                            <!-- 已选择的知识点将在这里动态显示（移动设备） -->
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <button class="btn btn-outline-danger btn-sm left-clear-btn" disabled>
                                <i class="fas fa-trash-alt mr-1"></i>清空所有知识点
                            </button>
                            <button class="btn btn-success left-generate-btn" disabled>
                                <i class="fas fa-magic mr-1"></i>生成试卷
                            </button>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 中间区域 -->
            <div class="col-lg-6 col-md-8">
                <!-- 已选择知识点区域 -->
                <div class="card mb-4" id="selected-knowledge-points-card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-check-circle mr-2"></i>已选择的知识点</h5>
                        <div>
                            <span class="badge badge-light selectedCount">0</span> 个知识点已选择
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info" id="no-selected-points-message">
                            <i class="fas fa-info-circle mr-2"></i>尚未选择任何知识点，请从左侧选择知识点
                        </div>

                        <!-- 搜索和排序工具栏 -->
                        <div class="selected-points-toolbar mb-3" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="input-group input-group-sm" style="max-width: 300px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="fas fa-search"></i>
                                        </span>
                                    </div>
                                    <input type="text" class="form-control" id="knowledgePointFilter" placeholder="搜索知识点...">
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-secondary sort-knowledge-points" data-sort="name">
                                        <i class="fas fa-sort-alpha-down mr-1"></i>名称
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary sort-knowledge-points" data-sort="count">
                                        <i class="fas fa-sort-numeric-down mr-1"></i>题量
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 已选择知识点容器 -->
                        <div id="selected-knowledge-points-container" class="row">
                            <!-- 已选择的知识点将在这里动态显示 -->
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-between align-items-center">
                        <button class="btn btn-outline-danger btn-sm" id="clearAllKnowledgePointsBtn" disabled>
                            <i class="fas fa-trash-alt mr-1"></i>清空所有知识点
                        </button>
                        <button class="btn btn-primary" id="generatePaperBtnBottom" disabled>
                            <i class="fas fa-magic mr-1"></i>生成试卷
                        </button>
                    </div>
                </div>


            </div>

            <!-- 右侧历史试卷区域 -->
            <div class="col-lg-3 d-none d-lg-block">
                <!-- 历史试卷 -->
                <div class="card">
                    <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history mr-2"></i>历史试卷 <span class="badge badge-light" id="historyCount">0</span></h5>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-light btn-sm" id="refreshHistoryBtn" title="刷新">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button type="button" class="btn btn-outline-light btn-sm" id="clearHistoryBtn" title="清空历史" disabled>
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="paperHistoryTableContainer">
                                <thead class="thead-light sticky-top">
                                    <tr>
                                        <th width="35%">标题</th>
                                        <th width="20%">时间</th>
                                        <th width="45%">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="paperHistoryTable">
                                    <tr>
                                        <td colspan="3" class="text-center py-5">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="sr-only">加载中...</span>
                                            </div>
                                            <p class="mt-2 text-muted">加载历史试卷...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer bg-light">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <small class="text-muted" id="paperHistoryInfo">
                                <i class="fas fa-info-circle mr-1"></i>
                                第 <span id="currentPageInfo">0</span>/<span id="totalPagesInfo">0</span> 页
                            </small>
                            <nav aria-label="历史试卷分页" id="paperHistoryPagination" class="mb-0"></nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 移动端历史试卷区域 -->
        <div class="row d-lg-none mt-4">
            <div class="col-12">
                <!-- 历史试卷 -->
                <div class="card">
                    <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history mr-2"></i>历史试卷 <span class="badge badge-light" id="historyCountMobile">0</span></h5>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-light btn-sm" id="refreshHistoryBtnMobile" title="刷新">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button type="button" class="btn btn-outline-light btn-sm" id="clearHistoryBtnMobile" title="清空历史" disabled>
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0" style="max-height: 300px; overflow-y: auto;">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="paperHistoryTableContainerMobile">
                                <thead class="thead-light sticky-top">
                                    <tr>
                                        <th width="30%">标题</th>
                                        <th width="20%">创建时间</th>
                                        <th width="10%">总分</th>
                                        <th width="40%">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="paperHistoryTableMobile">
                                    <!-- 移动端历史试卷内容将在这里显示 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted" id="paperHistoryInfoMobile">
                                <i class="fas fa-info-circle mr-1"></i>
                                显示第 <span id="currentPageInfoMobile">0</span> 页，共 <span id="totalPagesInfoMobile">0</span> 页
                            </small>
                            <nav aria-label="历史试卷分页" id="paperHistoryPaginationMobile" class="mb-0"></nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 试卷生成模态框 -->
    <div th:replace="paper/paper-generation-modal :: modal"></div>

    <!-- 自由组卷模态框 -->
    <div th:replace="paper/custom-generate-modal :: modal"></div>

    <!-- 版本帮助模态框 -->
    <div th:replace="paper/version-help-modal"></div>

    <!-- 智能组卷流程介绍模态框 -->
    <div th:replace="paper/algorithm-flow-modal :: modal"></div>

    <!--  配置加载模态框 -->
    <div class="modal fade" id="loadConfigModal" tabindex="-1" aria-labelledby="loadConfigModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="loadConfigModalLabel">
                        <i class="fas fa-folder-open me-2"></i>加载试卷配置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 搜索和筛选 -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="configSearchInput" placeholder="搜索配置名称...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includePublicConfigsModal">
                                <label class="form-check-label" for="includePublicConfigsModal">
                                    包含公共配置
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 配置列表 -->
                    <div class="config-list-container" style="max-height: 400px; overflow-y: auto;">
                        <div id="configListLoading" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">加载配置列表...</p>
                        </div>
                        <div id="configList" class="row" style="display: none;">
                            <!-- 配置卡片将在这里动态加载 -->
                        </div>
                        <div id="configListEmpty" class="text-center py-4" style="display: none;">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无配置</h5>
                            <p class="text-muted">您还没有保存任何配置</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="me-auto">
                        <input type="file" id="importConfigFile" accept=".json" style="display: none;">
                        <button type="button" class="btn btn-outline-info" id="importConfigBtn">
                            <i class="fas fa-upload me-1"></i>从文件导入
                        </button>
                        <button type="button" class="btn btn-outline-warning ms-2" id="clearSelectionBtn" style="display: none;">
                            <i class="fas fa-times-circle me-1"></i>清除选择
                        </button>
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelLoadConfigBtn">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="loadSelectedConfigBtn" disabled>
                        <i class="fas fa-check me-1"></i>加载选中配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!--  配置保存模态框 -->
    <div class="modal fade" id="saveConfigModal" tabindex="-1" aria-labelledby="saveConfigModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="saveConfigModalLabel">
                        <i class="fas fa-save me-2"></i>保存试卷配置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="saveConfigForm">
                        <div class="mb-3">
                            <label for="configName" class="form-label">配置名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="configName" required maxlength="100"
                                   placeholder="请输入配置名称">
                            <div class="form-text">最多100个字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="configDescription" class="form-label">配置描述</label>
                            <textarea class="form-control" id="configDescription" rows="3" maxlength="500"
                                      placeholder="请输入配置描述（可选）"></textarea>
                            <div class="form-text">最多500个字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="titleTemplate" class="form-label">
                                试卷标题模板
                                <i class="fas fa-question-circle text-info ms-1" data-bs-toggle="tooltip"
                                   title="占位符会在生成试卷时自动替换为实际内容"></i>
                            </label>
                            <input type="text" class="form-control" id="titleTemplate" maxlength="200"
                                   placeholder="例如：{{年级}}{{科目}}{{学期}}考试试卷">
                            <div class="form-text">
                                <div class="alert alert-info py-2 px-3 mb-2" style="font-size: 0.9rem;">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-info-circle me-2 text-info"></i>
                                        <strong>智能标题模板说明</strong>
                                    </div>
                                    <p class="mb-2">您可以使用以下"魔法词"，系统会自动替换为实际内容：</p>
                                    <div class="row g-2 mb-2">
                                        <div class="col-6">
                                            <span class="badge bg-primary me-1">{{科目}}</span>
                                            <small>→ 数学、语文、英语等</small>
                                        </div>
                                        <div class="col-6">
                                            <span class="badge bg-success me-1">{{年级}}</span>
                                            <small>→ 高一、初二、六年级等</small>
                                        </div>
                                        <div class="col-6">
                                            <span class="badge bg-warning me-1">{{学期}}</span>
                                            <small>→ 上学期、下学期、期末等</small>
                                        </div>
                                        <div class="col-6">
                                            <span class="badge bg-info me-1">{{类型}}</span>
                                            <small>→ 考试、练习、测验等</small>
                                        </div>
                                        <div class="col-6">
                                            <span class="badge bg-dark me-1">{{日期}}</span>
                                            <small>→ 2024-01-15等</small>
                                        </div>
                                    </div>
                                    <div class="bg-light p-2 rounded">
                                        <strong>示例：</strong>
                                        <br>输入：<code>{{年级}}{{科目}}{{学期}}考试试卷</code>
                                        <br>生成：<span class="text-success">高一数学期末考试试卷</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="setAsDefault">
                                    <label class="form-check-label" for="setAsDefault">
                                        设为默认配置
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="makePublic">
                                    <label class="form-check-label" for="makePublic">
                                        设为公共配置
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelSaveConfigBtn">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="saveConfigSubmitBtn">
                        <i class="fas fa-save me-1"></i>保存配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="spinner-border text-primary mb-4" style="width: 3rem; height: 3rem;" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <h5 id="loadingTitle" class="mb-3">正在处理</h5>
                    <p class="text-muted" id="loadingMessage">请稍候...</p>
                    <div class="progress mt-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery, Popper.js, Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>

    <!-- KaTeX for math formula rendering -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js" integrity="sha384-cpW21h6RZv/phavutF+AuVYrr+dA8xD9zs6FwLpaCct6O9ctzYFfFr4dgmgccOTx" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>

    <!-- 知识点搜索样式 -->
    <link rel="stylesheet" href="/static/css/knowledge-search.css">

    <!-- 调试样式 -->
    <style>
        .debug-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            z-index: 9999;
            display: none;
        }
        .debug-toggle {
            position: fixed;
            bottom: 10px;
            left: 10px;
            z-index: 9999;
        }
    </style>

    <!--  配置加载模态框选中效果样式 -->
    <style>
        /* 配置卡片基础样式 */
        .config-card-modal {
            transition: all 0.3s ease;
            border: 2px solid transparent;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        /* 悬停效果 */
        .config-card-modal:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
            border-color: rgba(0, 123, 255, 0.3);
        }

        /* 选中效果 */
        .config-card-modal.selected {
            border-color: #007bff;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0.1) 100%);
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 123, 255, 0.25);
        }

        /* 选中状态的标记 */
        .config-card-modal.selected::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
            z-index: 1;
        }

        /* 选中状态的勾选图标 - 简化动画效果 */
        .config-card-modal.selected::after {
            content: '\f00c';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            z-index: 2;
            opacity: 1;
            transition: opacity 0.2s ease;
        }

        /* 选中状态的卡片标题高亮 */
        .config-card-modal.selected .card-title {
            color: #007bff;
            font-weight: 600;
        }

        /* 选中状态的徽章样式 */
        .config-card-modal.selected .badge {
            background-color: #007bff;
            color: white;
        }

        /* 配置列表容器样式优化 */
        .config-list-container {
            padding: 10px;
        }

        /* 配置卡片间距 */
        .config-card-modal {
            margin-bottom: 15px;
        }

        /* 加载状态优化 */
        #configListLoading {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
            margin: 10px;
        }

        /* 空状态优化 */
        #configListEmpty {
            background: rgba(248, 249, 250, 0.5);
            border-radius: 8px;
            margin: 10px;
        }

        /* 搜索框聚焦效果 */
        #configSearchInput:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* 模态框标题样式 */
        #loadConfigModalLabel {
            color: #007bff;
        }

        /* 加载选中配置按钮样式 */
        #loadSelectedConfigBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        #loadSelectedConfigBtn:not(:disabled) {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        #loadSelectedConfigBtn:not(:disabled):hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        /* 按钮脉冲动画 */
        .btn-pulse {
            animation: btnPulse 0.6s ease-in-out;
        }

        @keyframes btnPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 6px 25px rgba(0, 123, 255, 0.5);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            }
        }

        /* 选中状态提示 - 简化动画 */
        .selection-hint {
            transition: opacity 0.2s ease;
        }

        /* 清除选择按钮样式 */
        #clearSelectionBtn {
            transition: all 0.3s ease;
            border-color: #ffc107;
            color: #856404;
        }

        #clearSelectionBtn:hover {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(255, 193, 7, 0.3);
        }

        /* 取消选择提示样式 */
        .config-card-modal.selected .selection-hint .text-muted {
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* 配置卡片基础样式 - 移除浮动提示以改善观感 */
        .config-card-modal {
            position: relative;
        }

        /* 历史试卷操作按钮样式 */
        .paper-actions {
            display: flex;
            gap: 2px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        /* 超小按钮样式 */
        .btn-xs {
            padding: 2px 6px;
            font-size: 10px;
            line-height: 1.2;
            border-radius: 3px;
            min-width: 24px;
            height: 24px;
        }

        /* 按钮颜色优化 */
        .paper-actions .btn-outline-primary:hover {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }

        .paper-actions .btn-outline-info:hover {
            background-color: #17a2b8;
            border-color: #17a2b8;
            color: white;
        }

        .paper-actions .btn-outline-success:hover {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }

        .paper-actions .btn-outline-warning:hover {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }

        .paper-actions .btn-outline-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        .paper-actions .btn-outline-danger:hover {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        /* 难度分布模态框样式 */
        #difficultyDistributionModal .difficulty-stat {
            padding: 15px;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.02);
            margin-bottom: 10px;
        }

        #difficultyDistributionModal .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        #difficultyDistributionModal .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        #difficultyDistributionModal .question-item {
            transition: all 0.2s ease;
            background: #f8f9fa;
        }

        #difficultyDistributionModal .question-item:hover {
            background: #e9ecef;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #difficultyDistributionModal .question-title {
            font-size: 0.9rem;
            color: #495057;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            vertical-align: middle;
        }

        #difficultyDistributionModal .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
        }

        #difficultyDistributionModal .nav-tabs .nav-link.active {
            background-color: #fff;
            border-bottom: 2px solid #007bff;
            color: #007bff;
        }

        #difficultyDistributionModal .tab-content {
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.25rem 0.25rem;
            padding: 15px;
            background: #fff;
        }

        /* 难度分布滚动条样式 */
        #difficultyDistributionModal .difficulty-questions::-webkit-scrollbar {
            width: 6px;
        }

        #difficultyDistributionModal .difficulty-questions::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        #difficultyDistributionModal .difficulty-questions::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        #difficultyDistributionModal .difficulty-questions::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>

    <!-- 公共JS -->
    <script src="/static/js/common.js"></script>
    <script src="/static/paper/generate-user-prefs.js"></script>
    <script src="/static/js/paper-generate.js"></script>
    <script src="/static/js/custom-paper.js"></script>
    <script src="/static/js/submit-paper.js"></script>
    <script src="/static/js/paper-preview.js"></script>
    <script src="/static/js/paper-real-time-preview.js"></script>
    <script src="/static/js/custom-paper-preview-integration.js"></script>
    <script src="/static/js/paper-version-selector.js"></script>
    <!-- 知识点搜索功能脚本 -->
    <script src="/static/js/knowledge-search.js"></script>
    <!-- 🧠 智能组卷流程介绍脚本 -->
    <script src="/static/js/algorithm-flow.js"></script>
    <!--  配置管理脚本 -->
    <script src="/static/js/paper-config-integration.js"></script>
    <!--  下拉菜单修复脚本 -->
    <script src="/static/js/dropdown-menu-fix.js"></script>

    <!-- 开发环境测试脚本 -->
    <script src="/static/js/knowledge-point-id-fix-test.js"></script>
    <script src="/static/js/knowledge-point-debug.js"></script>
    <script src="/static/js/real-time-preview-test.js"></script>
    <script src="/static/js/version-selector-debug.js"></script>

    <!-- KaTeX初始化脚本 -->
    <script>
        // 确保KaTeX在页面加载完成后可用
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，检查KaTeX状态...');

            if (typeof katex !== 'undefined') {
                console.log('✅ KaTeX已加载');
            } else {
                console.warn('⚠️ KaTeX未加载');
            }

            if (typeof renderMathInElement !== 'undefined') {
                console.log('✅ KaTeX auto-render已加载');

                // 为整个页面渲染数学公式
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\(', right: '\\)', display: false},
                        {left: '\\[', right: '\\]', display: true}
                    ],
                    throwOnError: false,
                    errorColor: '#cc0000',
                    strict: false
                });

                console.log('✅ 页面数学公式渲染完成');
            } else {
                console.warn('⚠️ KaTeX auto-render未加载');
            }

            // 检查测试脚本是否正确加载
            setTimeout(() => {
                console.log('🔍 检查测试脚本加载状态...');
                console.log('- checkAndFixPreview:', typeof window.checkAndFixPreview);
                console.log('- forceInitializePreview:', typeof window.forceInitializePreview);
                console.log('- runRealTimePreviewTests:', typeof window.runRealTimePreviewTests);
                console.log('- PaperRealTimePreview:', typeof window.PaperRealTimePreview);
                console.log('- CustomPaperPreviewIntegration:', typeof window.CustomPaperPreviewIntegration);
                console.log('- customPaperPreviewIntegration:', typeof window.customPaperPreviewIntegration);

                if (typeof window.checkAndFixPreview === 'function') {
                    console.log('✅ 测试脚本已正确加载');
                } else {
                    console.warn('⚠️ 测试脚本加载失败');
                }
            }, 2000);
        });

        //  修复所有模态框的滚动问题
        $(document).ready(function() {
            console.log('🔧 初始化模态框滚动修复...');

            // 监听所有模态框的显示事件
            $('.modal').on('show.bs.modal', function() {
                console.log('模态框显示，保持页面滚动功能');
                // 确保body可以滚动
                $('body').removeClass('modal-open').css({
                    'overflow': 'auto',
                    'padding-right': '0px'
                });
            });

            // 监听所有模态框的隐藏事件
            $('.modal').on('hidden.bs.modal', function() {
                console.log('模态框隐藏，恢复正常滚动');
                // 确保body可以滚动
                $('body').removeClass('modal-open').css({
                    'overflow': 'auto',
                    'padding-right': '0px'
                });

                // 移除可能的backdrop
                $('.modal-backdrop').remove();
            });

            // 监听所有模态框的显示后事件
            $('.modal').on('shown.bs.modal', function() {
                console.log('模态框显示完成，强制启用滚动');
                // 强制启用滚动
                $('body').css({
                    'overflow-y': 'auto',
                    'overflow-x': 'hidden'
                });

                // 确保容器可以滚动
                $('.container-fluid').css({
                    'overflow': 'visible',
                    'height': 'auto'
                });
            });

            console.log('✅ 模态框滚动修复初始化完成');
        });

        //  全局紧急修复函数 - 可在控制台调用
        window.fixAllModalScrollIssues = function() {
            console.log('🔧 修复所有模态框滚动问题...');

            // 关闭所有模态框
            $('.modal').modal('hide');

            // 移除所有backdrop
            $('.modal-backdrop').remove();

            // 恢复body样式
            $('body').removeClass('modal-open').css({
                'overflow': 'auto',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
                'padding-right': '0px',
                'margin-right': '0px'
            });

            // 恢复容器样式
            $('.container-fluid').css({
                'overflow': 'visible',
                'height': 'auto',
                'min-height': '100vh'
            });

            console.log('✅ 所有滚动问题修复完成');

            // 显示成功提示
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: '修复完成',
                    text: '页面滚动功能已恢复正常',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        };
    </script>

    <!-- 知识点搜索功能初始化 -->
    <script>
        $(document).ready(function() {
            // 确保搜索功能正确初始化
            console.log('🔍 初始化知识点搜索功能...');

            // 检查函数是否存在
            if (typeof initMainPageSearch === 'function') {
                // 初始化搜索功能
                initMainPageSearch();
                console.log('✅ 知识点搜索功能初始化完成');
            } else {
                console.error('❌ 搜索功能初始化失败: initMainPageSearch 函数未找到');

                // 如果主搜索函数不存在，尝试使用备用的搜索功能
                console.log('🔄 尝试使用备用搜索功能...');

                // 直接绑定搜索事件
                $('#searchBtnMain').off('click.search').on('click.search', function() {
                    const searchTerm = $('#knowledgePointSearchMain').val().trim();
                    if (searchTerm) {
                        console.log('🔍 执行搜索:', searchTerm);
                        // 调用API搜索
                        $.ajax({
                            url: '/api/knowledge/search',
                            method: 'GET',
                            data: { keyword: searchTerm },
                            success: function(response) {
                                console.log('搜索成功:', response);
                                if (response && response.success && response.data) {
                                    // 显示搜索结果
                                    displayKnowledgeSearchResults(response.data, searchTerm);
                                } else {
                                    console.error('搜索失败:', response);
                                    alert('搜索失败: ' + (response.message || '未知错误'));
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('搜索请求失败:', {xhr, status, error});
                                alert('搜索请求失败，请检查网络连接');
                            }
                        });
                    } else {
                        alert('请输入搜索关键词');
                    }
                });

                // 回车键搜索
                $('#knowledgePointSearchMain').off('keypress.search').on('keypress.search', function(e) {
                    if (e.which === 13) {
                        e.preventDefault();
                        $('#searchBtnMain').click();
                    }
                });

                console.log('✅ 备用搜索功能初始化完成');
            }
        });

        // 显示搜索结果的函数
        function displayKnowledgeSearchResults(results, searchTerm) {
            const container = $('#knowledge-points-container');

            if (!results || results.length === 0) {
                container.html(`
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">未找到匹配的知识点</h5>
                        <p class="text-muted">搜索关键词: "${searchTerm}"</p>
                        <button class="btn btn-primary" onclick="$('#clearSearchMain').click()">
                            <i class="fas fa-arrow-left mr-1"></i>返回浏览
                        </button>
                    </div>
                `);
                return;
            }

            let html = `
                <div class="search-results">
                    <div class="alert alert-info">
                        <i class="fas fa-search mr-2"></i>
                        找到 <strong>${results.length}</strong> 个匹配的知识点
                    </div>
                    <div class="row">
            `;

            results.forEach(item => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6 class="card-title">${item.knowledgeName || item.name || '未命名'}</h6>
                                <p class="card-text text-muted small">${item.groupName || '未分类'}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge badge-info">${item.topicCount || 0} 题</span>
                                    <div class="form-check">
                                        <input class="form-check-input knowledge-checkbox" type="checkbox"
                                               value="${item.knowledgeId || item.id}"
                                               data-name="${item.knowledgeName || item.name || ''}">
                                        <label class="form-check-label">选择</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;

            container.html(html);

            // 显示清空按钮
            $('#clearSearchMain').show();
        }
    </script>
</body>
</html>
