/**
 * 知识点ID稳定性修复测试脚本
 * 
 * 问题描述：
 * 用户选择知识点后，切换到其他知识点分类页面时，
 * 已选择知识点的ID会发生变化，导致数据不一致。
 * 
 * 修复方案：
 * 1. 在首次选择知识点时，立即获取并存储真实的knowId
 * 2. 后续渲染时使用存储的knowId，不再从当前页面动态获取
 * 3. 确保知识点ID在整个会话期间保持稳定
 */

// 测试函数：验证知识点ID稳定性
function testKnowledgePointIdStability() {
    console.log('=== 开始测试知识点ID稳定性 ===');
    
    // 1. 清空当前选择
    selectedKnowledgePoints.clear();
    console.log('1. 已清空所有选择的知识点');
    
    // 2. 模拟选择第一个知识点
    const firstCheckbox = $('.knowledge-checkbox').first();
    if (firstCheckbox.length > 0) {
        const firstId = firstCheckbox.val();
        const firstName = firstCheckbox.closest('.card').find('.card-title').text().trim();
        
        firstCheckbox.prop('checked', true).trigger('change');
        console.log(`2. 选择第一个知识点: ${firstName} (ID: ${firstId})`);
        
        // 记录初始状态
        const initialData = selectedKnowledgePoints.get(firstId);
        console.log('   初始数据:', initialData);
        
        // 3. 切换到其他知识点分类
        setTimeout(() => {
            const categoryButtons = $('.knowledge-category-btn');
            if (categoryButtons.length > 1) {
                const secondCategory = categoryButtons.eq(1);
                secondCategory.click();
                console.log(`3. 切换到其他分类: ${secondCategory.text()}`);
                
                // 4. 等待页面加载后检查数据稳定性
                setTimeout(() => {
                    const afterSwitchData = selectedKnowledgePoints.get(firstId);
                    console.log('   切换后数据:', afterSwitchData);
                    
                    // 5. 验证ID是否保持稳定
                    if (initialData && afterSwitchData) {
                        const idStable = initialData.knowId === afterSwitchData.knowId;
                        const nameStable = initialData.name === afterSwitchData.name;
                        
                        console.log(`5. ID稳定性检查:`);
                        console.log(`   - knowId稳定: ${idStable} (${initialData.knowId} -> ${afterSwitchData.knowId})`);
                        console.log(`   - name稳定: ${nameStable} (${initialData.name} -> ${afterSwitchData.name})`);
                        
                        if (idStable && nameStable) {
                            console.log('✅ 测试通过：知识点ID保持稳定');
                        } else {
                            console.log('❌ 测试失败：知识点ID发生变化');
                        }
                    } else {
                        console.log('❌ 测试失败：知识点数据丢失');
                    }
                    
                    // 6. 测试试卷生成
                    testPaperGeneration();
                    
                }, 1000);
            } else {
                console.log('3. 跳过分类切换测试（只有一个分类）');
                testPaperGeneration();
            }
        }, 500);
    } else {
        console.log('❌ 测试失败：没有找到知识点复选框');
    }
}

// 测试试卷生成时的ID一致性
function testPaperGeneration() {
    console.log('\n=== 测试试卷生成ID一致性 ===');
    
    if (selectedKnowledgePoints.size === 0) {
        console.log('❌ 无法测试：没有选择的知识点');
        return;
    }
    
    // 模拟生成试卷配置
    const knowledgeConfigs = [];
    const processedIds = new Set();
    
    selectedKnowledgePoints.forEach(function(pointData, pointId) {
        const knowledgeId = parseInt(pointId);
        
        if (processedIds.has(knowledgeId)) {
            console.warn(`发现重复的知识点ID: ${knowledgeId}`);
            return;
        }
        processedIds.add(knowledgeId);
        
        const knowId = pointData.knowId || knowledgeId;
        
        knowledgeConfigs.push({
            knowledgeId: knowId,
            questionCount: 5,
            includeShortAnswer: true
        });
        
        console.log(`配置知识点: ${pointData.name} (pointId: ${pointId}, knowId: ${knowId})`);
    });
    
    console.log(`生成配置数量: ${knowledgeConfigs.length}`);
    console.log('配置详情:', knowledgeConfigs);
    
    if (knowledgeConfigs.length === selectedKnowledgePoints.size) {
        console.log('✅ 试卷生成测试通过：配置数量正确');
    } else {
        console.log('❌ 试卷生成测试失败：配置数量不匹配');
    }
}

// 测试重复选择检测
function testDuplicateDetection() {
    console.log('\n=== 测试重复选择检测 ===');
    
    const originalSize = selectedKnowledgePoints.size;
    console.log(`当前选择数量: ${originalSize}`);
    
    // 尝试重复添加相同的知识点
    selectedKnowledgePoints.forEach(function(pointData, pointId) {
        console.log(`尝试重复添加: ${pointData.name} (ID: ${pointId})`);
        
        // 模拟重复选择
        if (!selectedKnowledgePoints.has(pointId)) {
            selectedKnowledgePoints.set(pointId, pointData);
            console.log('❌ 重复检测失败：允许了重复添加');
        } else {
            console.log('✅ 重复检测正常：阻止了重复添加');
        }
    });
    
    const afterSize = selectedKnowledgePoints.size;
    if (originalSize === afterSize) {
        console.log('✅ 重复检测测试通过：数量保持不变');
    } else {
        console.log('❌ 重复检测测试失败：数量发生变化');
    }
}

// 测试清理功能
function testCleanupFunction() {
    console.log('\n=== 测试清理功能 ===');
    
    // 人工创建重复数据进行测试
    const testData = new Map(selectedKnowledgePoints);
    
    // 添加一个重复项
    if (testData.size > 0) {
        const firstEntry = testData.entries().next().value;
        const [firstId, firstData] = firstEntry;
        
        // 创建一个重复的条目
        testData.set(firstId + '_duplicate', firstData);
        console.log(`添加测试重复项: ${firstData.name}_duplicate`);
        
        // 运行清理函数
        console.log(`清理前数量: ${testData.size}`);
        
        // 模拟清理逻辑
        const uniqueSelections = new Map();
        const seenNames = new Set();
        
        testData.forEach((pointData, pointId) => {
            const key = `${pointData.name}_${pointData.knowId || pointData.id}`;
            if (!seenNames.has(key)) {
                seenNames.add(key);
                uniqueSelections.set(pointId, pointData);
            } else {
                console.log(`发现重复项: ${pointData.name} (${pointId})`);
            }
        });
        
        console.log(`清理后数量: ${uniqueSelections.size}`);
        
        if (uniqueSelections.size < testData.size) {
            console.log('✅ 清理功能测试通过：成功检测并清理重复项');
        } else {
            console.log('❌ 清理功能测试失败：未检测到重复项');
        }
    } else {
        console.log('跳过清理测试：没有数据');
    }
}

// 主测试函数
function runAllTests() {
    console.log('🧪 开始运行知识点ID稳定性测试套件');
    console.log('请确保页面上有可选择的知识点');
    
    // 依次运行所有测试
    testKnowledgePointIdStability();
    
    // 延迟运行其他测试，等待第一个测试完成
    setTimeout(() => {
        testDuplicateDetection();
        testCleanupFunction();
        
        console.log('\n🎯 测试套件运行完成');
        console.log('请检查上述测试结果，确保所有测试都通过');
    }, 3000);
}

// 导出测试函数供控制台使用
window.testKnowledgePointIdStability = testKnowledgePointIdStability;
window.testPaperGeneration = testPaperGeneration;
window.testDuplicateDetection = testDuplicateDetection;
window.testCleanupFunction = testCleanupFunction;
window.runAllTests = runAllTests;

// 自动运行测试（可选）
// setTimeout(runAllTests, 2000);

console.log('📋 知识点ID稳定性测试脚本已加载');
console.log('可用的测试函数:');
console.log('- testKnowledgePointIdStability(): 测试ID稳定性');
console.log('- testPaperGeneration(): 测试试卷生成');
console.log('- testDuplicateDetection(): 测试重复检测');
console.log('- testCleanupFunction(): 测试清理功能');
console.log('- runAllTests(): 运行所有测试');
console.log('');
console.log('使用方法: 在控制台中调用 runAllTests() 开始测试');
