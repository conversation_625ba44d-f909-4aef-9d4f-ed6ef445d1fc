package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.dto.KnowledgePointDto;
import com.edu.maizi_edu_sys.dto.SimpleKnowledgePointDto;
import com.edu.maizi_edu_sys.entity.KnowledgePoint;
import com.edu.maizi_edu_sys.repository.KnowledgePointRepository;
import com.edu.maizi_edu_sys.service.KnowledgePointService;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class KnowledgePointServiceImpl implements KnowledgePointService {

    private final KnowledgePointRepository knowledgePointRepository;
    private final ModelMapper modelMapper;

    @Autowired
    public KnowledgePointServiceImpl(KnowledgePointRepository knowledgePointRepository, ModelMapper modelMapper) {
        this.knowledgePointRepository = knowledgePointRepository;
        this.modelMapper = modelMapper;
    }

    @Override
    public List<SimpleKnowledgePointDto> getAllSimpleKnowledgePoints() {
        LambdaQueryWrapper<KnowledgePoint> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KnowledgePoint::getIsDeleted, false);
        List<KnowledgePoint> knowledgePoints = knowledgePointRepository.selectList(wrapper);
        return knowledgePoints.stream()
            .map(kp -> modelMapper.map(kp, SimpleKnowledgePointDto.class))
            .collect(Collectors.toList());
    }

    @Override
    public List<KnowledgePoint> findByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<KnowledgePoint> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(KnowledgePoint::getId, ids).eq(KnowledgePoint::getIsDeleted, false);
        return knowledgePointRepository.selectList(wrapper);
    }

    @Override
    public long countByClassificationId(Integer classificationId) {
        if (classificationId == null) {
            return 0;
        }
        LambdaQueryWrapper<KnowledgePoint> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KnowledgePoint::getClassificationId, classificationId)
               .eq(KnowledgePoint::getIsDeleted, false);
        return knowledgePointRepository.selectCount(wrapper);
    }

    @Override
    public List<KnowledgePointDto> findKnowledgePointsByClassificationId(Integer classificationId, Integer pageNum, Integer pageSize) {
        if (classificationId == null) {
            return new ArrayList<>();
        }

        pageNum = (pageNum == null || pageNum < 1) ? 1 : pageNum;
        pageSize = (pageSize == null || pageSize <= 0) ? 10 : pageSize;

        Page<KnowledgePoint> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<KnowledgePoint> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KnowledgePoint::getClassificationId, classificationId)
               .eq(KnowledgePoint::getIsDeleted, false)
               .orderByAsc(KnowledgePoint::getId);

        Page<KnowledgePoint> resultPage = knowledgePointRepository.selectPage(page, wrapper);

        return resultPage.getRecords().stream()
                .map(kp -> modelMapper.map(kp, KnowledgePointDto.class))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public KnowledgePointDto createKnowledgePoint(KnowledgePointDto knowledgePointDto) {
        if (knowledgePointDto == null) {
            throw new IllegalArgumentException("知识点数据不能为空");
        }

        KnowledgePoint knowledgePoint = modelMapper.map(knowledgePointDto, KnowledgePoint.class);
        knowledgePoint.setIsDeleted(false);
        knowledgePoint.setCreateTime(LocalDateTime.now());

        knowledgePointRepository.insert(knowledgePoint);

        return modelMapper.map(knowledgePoint, KnowledgePointDto.class);
    }

    @Override
    @Transactional
    public KnowledgePointDto updateKnowledgePoint(KnowledgePointDto knowledgePointDto) {
        if (knowledgePointDto == null || knowledgePointDto.getId() == null) {
            throw new IllegalArgumentException("更新的知识点数据或ID不能为空");
        }

        KnowledgePoint existingKnowledgePoint = knowledgePointRepository.selectById(knowledgePointDto.getId());
        if (existingKnowledgePoint == null || existingKnowledgePoint.getIsDeleted()) {
            throw new RuntimeException("知识点不存在或已被删除: " + knowledgePointDto.getId());
        }

        modelMapper.map(knowledgePointDto, existingKnowledgePoint);
        existingKnowledgePoint.setUpdateTime(LocalDateTime.now());
        existingKnowledgePoint.setIsDeleted(false);

        knowledgePointRepository.updateById(existingKnowledgePoint);

        return modelMapper.map(existingKnowledgePoint, KnowledgePointDto.class);
    }

    @Override
    @Transactional
    public void deleteKnowledgePoint(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("知识点ID不能为空");
        }

        KnowledgePoint knowledgePoint = knowledgePointRepository.selectById(id);
        if (knowledgePoint == null || knowledgePoint.getIsDeleted()) {
            throw new RuntimeException("知识点不存在或已被删除: " + id);
        }

        knowledgePoint.setIsDeleted(true);
        knowledgePoint.setUpdateTime(LocalDateTime.now());
        knowledgePointRepository.updateById(knowledgePoint);
    }
}