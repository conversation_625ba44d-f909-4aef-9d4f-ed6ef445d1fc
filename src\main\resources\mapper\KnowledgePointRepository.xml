<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.maizi_edu_sys.repository.KnowledgePointRepository">

    <!-- 获取有题目的知识点ID列表 -->
    <select id="findKnowledgePointsWithQuestions" resultType="java.lang.Long">
        SELECT DISTINCT kp.id
        FROM knowledge_point kp
        JOIN topic_bak t ON CAST(kp.id AS UNSIGNED) = t.know_id
        WHERE kp.id IN
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND kp.is_deleted = false
    </select>

    <!-- 获取知识点题目数量统计 -->
    <select id="countQuestionsByKnowledgePoints" resultType="java.util.Map">
        SELECT 
            kp.id as knowledgeId,
            COUNT(t.id) as questionCount
        FROM 
            knowledge_point kp
        LEFT JOIN 
            topic_bak t ON CAST(kp.id AS UNSIGNED) = t.know_id
        WHERE 
            kp.id IN
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND kp.is_deleted = false
        GROUP BY 
            kp.id
    </select>

    <!-- 获取有简答题的知识点ID列表 -->
    <select id="findKnowledgePointsWithShortAnswerQuestions" resultType="java.lang.Long">
        SELECT DISTINCT kp.id
        FROM knowledge_point kp
        JOIN topic_bak t ON CAST(kp.id AS UNSIGNED) = t.know_id
        WHERE kp.id IN
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND kp.is_deleted = false
        AND t.type = 'short'
    </select>

    <!-- 获取知识点的题目类型统计 -->
    <select id="countQuestionTypesByKnowledgePoints" resultType="java.util.Map">
        SELECT 
            kp.id as knowledgeId,
            t.type as questionType,
            COUNT(t.id) as questionCount
        FROM 
            knowledge_point kp
        JOIN 
            topic_bak t ON CAST(kp.id AS UNSIGNED) = t.know_id
        WHERE 
            kp.id IN
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND kp.is_deleted = false
        GROUP BY 
            kp.id, t.type
    </select>

</mapper>
