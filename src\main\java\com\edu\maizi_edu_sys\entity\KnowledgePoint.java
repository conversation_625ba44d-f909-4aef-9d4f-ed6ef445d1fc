package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
// import lombok.experimental.Accessors; // Uncomment if you use chaining setters

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
// @Accessors(chain = true) // Uncomment if you use chaining setters
@TableName("knowledge_point") // Make sure this matches your table name
public class KnowledgePoint implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO) // Assuming auto-increment ID
    private Long id;

    @TableField("name")
    private String name;

    @TableField("classification_id")
    private Integer classificationId;

    // Add other fields relevant to a knowledge point, e.g.:
    // @TableField("description")
    // private String description;

    // @TableField("subject_id") // If you link to subjects
    // private Integer subjectId;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableLogic // For logical delete
    @TableField("is_deleted")
    private Boolean isDeleted;

    // Default constructor (Lombok @Data handles this if no other constructor is defined)
    // public KnowledgePoint() {}

    // Getters and setters are handled by Lombok @Data
}