2025-05-28 13:01:34.807 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator.
2025-05-28 13:01:51.464 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 13:01:51.582 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 13:01:55.209 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 6, 题型配置: {SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:01:55.826 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 83, 总可用题目: 4570, 警告数量: 0
2025-05-28 13:02:07.118 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 6, 题型配置: {SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:02:07.421 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 83, 总可用题目: 4570, 警告数量: 0
2025-05-28 13:10:51.950 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 6, 题型配置: {SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:10:52.161 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 83, 总可用题目: 4570, 警告数量: 0
2025-05-28 13:28:55.259 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator.
2025-05-28 13:28:59.076 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 13:28:59.234 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 13:30:02.716 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 6, 题型配置: {SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:30:03.435 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 83, 总可用题目: 4570, 警告数量: 0
2025-05-28 13:30:09.594 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 13:30:09.612 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 13:30:19.899 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 6, 题型配置: {SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:30:20.530 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 83, 总可用题目: 4570, 警告数量: 0
2025-05-28 13:33:29.508 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 13:33:29.519 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 13:33:42.487 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20}
2025-05-28 13:33:42.561 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 20, 总可用题目: 741, 警告数量: 0
2025-05-28 13:33:43.520 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20}
2025-05-28 13:33:43.581 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 20, 总可用题目: 741, 警告数量: 0
2025-05-28 13:33:46.689 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10}
2025-05-28 13:33:46.755 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 30, 总可用题目: 767, 警告数量: 0
2025-05-28 13:33:47.559 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10}
2025-05-28 13:33:47.630 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 30, 总可用题目: 767, 警告数量: 0
2025-05-28 13:33:48.842 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=30}
2025-05-28 13:33:48.906 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 60, 总可用题目: 1661, 警告数量: 0
2025-05-28 13:33:51.551 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10}
2025-05-28 13:33:51.612 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 40, 总可用题目: 1661, 警告数量: 0
2025-05-28 13:33:54.677 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:33:54.754 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 42, 总可用题目: 1661, 警告数量: 1
2025-05-28 13:33:58.517 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:33:58.583 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 42, 总可用题目: 1661, 警告数量: 1
2025-05-28 13:34:00.071 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:34:00.130 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 42, 总可用题目: 1661, 警告数量: 1
2025-05-28 13:34:05.729 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:34:05.788 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 42, 总可用题目: 1661, 警告数量: 1
2025-05-28 13:35:34.339 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 13:35:34.361 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 13:35:38.296 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:35:38.392 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 42, 总可用题目: 1661, 警告数量: 1
2025-05-28 13:37:39.012 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-05-28 13:37:39.022 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=132, pages=27, current=1, size=5, records=5
2025-05-28 13:37:45.115 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:37:45.186 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 42, 总可用题目: 1661, 警告数量: 1
2025-05-28 13:43:07.586 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:43:07.693 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 42, 总可用题目: 1661, 警告数量: 1
2025-05-28 13:43:21.963 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始生成试卷预览，知识点配置数量: 2, 题型配置: {SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:43:22.053 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 试卷预览生成完成，总请求题目: 42, 总可用题目: 1661, 警告数量: 1
