2025-05-28 13:01:24.365 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on USER-20230226QO with PID 10464 (C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes started by Administrator in C:\Users\<USER>\IdeaProjects\maizi_edu_sys)
2025-05-28 13:01:24.372 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-28 13:01:24.373 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-28 13:01:28.367 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-28 13:01:28.367 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-28 13:01:28.368 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-28 13:01:28.593 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-28 13:01:29.489 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-28 13:01:30.001 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-28 13:01:30.998 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-28 13:01:30.998 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-28 13:01:30.998 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-28 13:01:33.336 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads, avatar=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\avatars
2025-05-28 13:01:33.344 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-28 13:01:33.489 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-28 13:01:34.406 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-28 13:01:34.985 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-28 13:01:35.383 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\
2025-05-28 13:01:36.149 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-28 13:01:36.229 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 12.503 seconds (JVM running for 13.687)
2025-05-28 13:01:36.257 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-28 13:01:36.261 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-28 13:01:36.261 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-28 13:01:36.261 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-28 13:01:36.262 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - user_id: bigint
2025-05-28 13:01:36.262 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-28 13:01:36.262 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-28 13:01:36.262 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-28 13:01:36.262 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-28 13:01:36.262 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-28 13:01:36.263 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty_distribution: json
2025-05-28 13:01:36.263 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-28 13:01:36.263 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-28 13:01:36.263 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - paper_type: varchar
2025-05-28 13:01:36.263 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - file_format: varchar
2025-05-28 13:01:36.263 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - download_count: int
2025-05-28 13:01:36.264 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - last_download_time: datetime
2025-05-28 13:01:36.264 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-28 13:01:36.264 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-28 13:01:36.264 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: tinyint
2025-05-28 13:01:36.274 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, title= 识记类 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=1740, actual_total_score=null, difficulty=0.5, difficulty_distribution=[{"easy": 0.2, "hard": 0.1, "medium": 0.7}], content=1,2,3,4,5,6,7,8,9,10,11,12, config={"totalScore":72,"typeScoreMap":{"choice":5,"multiple":8,"judge":3,"fill":4,"short":10},"questionTypes":{"choice":3,"multiple":2,"judge":2,"fill":3,"short":2}}, paper_type=regular, file_format=pdf, download_count=28, last_download_time=2025-05-24T19:14:30, create_time=2025-05-16T21:40:57, update_time=2025-05-24T19:14:30, is_deleted=false}, {id=2, title= 识记类 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=1740, actual_total_score=null, difficulty=0.6999999999999966, difficulty_distribution=null, content=103797,103802,103813,103825,103826,103831,103834,103839,103856,103868,103893,103902,103905,103915,103921,103929,103936,103958,103965,103972,103984,103989,103997,104003,104016,104021,104029,104045,104050,104106,104115,104138,104156,104159,104188,104203,104207,104212,104215,104218,104224,104233,104234,104252,104262,104286,104890,104893,104897,104918,104925,104931,104943,104957,104962,104990,104998,105003,105021,105048,105051,105061,105071,105088,105102,105112,105116,105155,105161,105167,105171,105181,105190,105195,105207,105210,105216,105229,105234,105244,105248,105255,105257,105279,105285,105288,105296,105299,105306,105315,105317,106862,106864,106874,107004,107015,107050,107163,107165,107168,107173,107186,107222,107234,107264,107268,107401,107540,107542,107583,107587,107592,107611,107615,107618,107621,107622,107636,107643,107652,107657,107682,107684,107686,107690,107691,107693,107696,107700,107701,107709,107717,107725,107739,107744,107749,107753,107758,107761,107764,107777,107779,107781,107782,107786,107788,107790,107791,107792,107793,107794,107795,107801,107802,107804,107805,107808,107809,107810,107812,107814,107815,107816,107817,107824,107825,107829,107832,107834,107836,107850,107851,107852,107870,107872,107882,107888,107929,107942,107993,108012,108082,108131,108200,108364,108382,108384,108385,108389,108411,108417,108421,108440,108442,108445,108475,108509,108534,108569,108585,108590,108819,108837,108845,108879,108940,109022,109030,109034,109036,109050,109074,109082,109152,109220,109226,109227,109234,109314,109343,109417,109419,109543,109545,109579,109592,109649,109650,109697,109703,109704,109705,109708,109714,109718,109721,109731,109767,109769,109771,110009,110016,110168,110172,110175,110268,110292,110471,110489,110494,110502,110509,110525,110562,110576,110724,110731,110734,110738,110751,110756,110762,110777,110784,110791,110977,111018,111025,111032,111053,111061,111230,111249,111335,111350,111357,111449,111463,111465,111475,111485,111488,111489,111504,111514,111561,111563,111564,111566,111567,111571,111572,111603,111604,111619,111627,111628,111632,111633,111635,111638,111642,111648,111649,111655,111657,111663,111692,111694,111695,111697,111720,111731,111751,111757,111798,111822,111858,112003,112009,112029,112035,112044,112047,112192,112212,112303,112310,112321,112325,112329,112336,112349,112360,112420,112517,112524,112529,112530,112532,112554,112574,112595,112606,112613,112616,112621,112625,112627,112628,112635,112830,112831,113331,113347,113353,113368,113374,113381,113401,113419,113422,113430,113433,113439,113483,113523,113524,113544,113552,113554,113560,113563,113568,113572,113655,113747,113796,113802,113883,113925,113929,113935,113943,113951,113959,113975,113977,113981,113985,114010,114024,114030,114037,114119,114124,114135,114173,115006,115033,115056,115062,115064,115066,115177,115191,115202,115209,115219,115240,115248,115257,115303,115309,115336,115352,115366,115447,115567,115570,115620,115650,115668,115679,115688,115693,115707,115723,115730,115740,115744,115745,115753,115772,115806,115814,115820,115923,115927,115938,115947,115955,115968,116073,116076,116127,116131,116335,116340,116345,116442,116721,116722,116991,116996,116999,117004,117007,117014,117019,117027,117033,117199,117230,117239,117484,117491,117503,117507,117510,117672,117685,117693,117694,117777,117842,117845,117856,117977,117981,118120,118129,118185,118193,118195,118196,118197,118200,118201,118202,118242,118251,118254,119853,119856,119858,119862,119864,119865,123783,123802,123875,123986,131454,131455,131456,131492,131498,131501,131505,131507,131510,131513,131527,131531,131532,131533,131534,131535,131540,131541,131543,131547,131552,131555,131560,131562,131569,131573,131578,131584,131590,131592,131598,131602,131604,131606,131616,131617,131624,131628,131630,131637,131643,131645,131646,131647,131648,131652,131727,131728,131734,131737,131739,131749,131750,131754,131760,131762,131763,131768,131769,131785,131791,131795,131798,131806,131809,131814,131817,131818,131823,131824,131825,131827,131828,131830,131831,133520,160715, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:44:07, update_time=2025-05-16T21:44:07, is_deleted=false}, {id=3, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=99, actual_total_score=null, difficulty=0.5, difficulty_distribution=null, content=126658,126668,126680,126686,126714,126718,126873,126899,126949,126953,126957,126960,126971,126975,126976,126981,126985,126989,126996,126998,127009,127094,127124,127132,127139,127178,127193,127197,127310,127316,127467,127479,127534, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:53:09, update_time=2025-05-16T21:53:09, is_deleted=false}, {id=4, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=105, actual_total_score=null, difficulty=0.7, difficulty_distribution=null, content=126658,126663,126723,126818,126853,126882,126890,126950,126952,126953,126963,126964,126968,126975,126980,126981,126986,127001,127007,127011,127049,127069,127083,127138,127142,127151,127153,127155,127161,127170,127182,127313,127457,127473,127505, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:57:20, update_time=2025-05-16T21:57:20, is_deleted=true}, {id=5, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=99, actual_total_score=null, difficulty=0.6928571428571428, difficulty_distribution=null, content=126641,126680,126718,126728,126762,126840,126845,126873,126890,126907,126914,126934,126959,126968,126975,126976,126984,126985,126991,127001,127011,127083,127111,127139,127148,127153,127157,127166,127174,127178,127501,127513,127532, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:59:54, update_time=2025-05-16T21:59:54, is_deleted=false}]
2025-05-28 13:01:36.366 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 136
2025-05-28 13:01:48.106 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODM0NzQ1NywiZXhwIjoxNzQ4NDMzODU3fQ.Tyyp1nzT-Ad4D8rlJ8q8xc3gmoLzbFAOWSYoFohDDtn_ILyDzZeRD-x5Lqhr5aCJ9U4LCZrMSVCyFKmNpN6oSA] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-28 13:01:48.116 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-28 13:01:49.343 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 13:01:49.344 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.429 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 13:01:49.430 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...6oSA
2025-05-28 13:01:49.430 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.432 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.433 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 13:01:49.483 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 13:01:49.484 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:01:49.673 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 13:01:49.674 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.677 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 13:01:49.677 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...6oSA
2025-05-28 13:01:49.677 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.679 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.680 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 13:01:49.686 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 13:01:49.686 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:01:49.704 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/history, Token: exists
2025-05-28 13:01:49.704 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.709 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Getting chat history for token: eyJhbGciOi...
2025-05-28 13:01:49.711 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /main/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 13:01:49.712 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Template path access: /main/avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:01:49.719 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.720 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 13:01:49.847 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Chat history response status: 200, size: not null
2025-05-28 13:01:49.870 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/32, Token: exists
2025-05-28 13:01:49.870 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.881 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Getting chat detail: chatId=32
2025-05-28 13:01:49.881 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.945 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Chat detail response: ApiResponse(code=200, message=success, data=ChatSession(id=32, userId=1920280447393230850, title=思想政治, knowId=218, bookUrl=https://www.yuque.com/shi-xi-qiang/kb/ofwnlgwerdm6u49i?singleDoc# 《普通高中教科书·思想政治必修1 中国特色社会主义_1-24》, createdAt=2025-05-14T16:55:25, updatedAt=2025-05-20T15:33:12, deleted=false))
2025-05-28 13:01:49.953 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/messages/32, Token: exists
2025-05-28 13:01:49.953 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:49.956 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Getting chat messages for chatId=32 (using /messages/{chatId} endpoint)
2025-05-28 13:01:49.968 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Chat messages response status: 200, data null? no
2025-05-28 13:01:50.103 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /main/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 13:01:50.103 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Template path access: /main/avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:01:50.916 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 13:01:50.916 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:50.918 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 13:01:50.918 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...6oSA
2025-05-28 13:01:50.918 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:50.920 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:50.921 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 13:01:50.926 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 13:01:50.926 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:01:51.088 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 13:01:51.444 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-28 13:01:51.444 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:51.445 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-28 13:01:51.445 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:51.446 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-05-28 13:01:51.456 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-05-28 13:01:51.924 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:01:51.924 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:51.926 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:01:51.926 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:51.928 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:51.933 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:01:52.689 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32, Token: exists
2025-05-28 13:01:52.689 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:52.690 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:01:52.690 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:52.691 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:52.697 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:01:52.714 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32/use, Token: exists
2025-05-28 13:01:52.714 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:52.715 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:01:52.715 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:52.716 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:52.720 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:01:52.738 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 配置使用记录更新，ID: 32, 使用次数: 65
2025-05-28 13:01:53.728 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:01:53.728 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:53.731 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:01:55.195 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:01:55.195 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:01:55.208 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=6, 题型配置={SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:02:01.849 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:02:01.849 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:01.851 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:02:01.851 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:01.852 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:01.857 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:02:05.009 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32, Token: exists
2025-05-28 13:02:05.009 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:05.011 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:02:05.011 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:05.012 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:05.017 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:02:05.030 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32/use, Token: exists
2025-05-28 13:02:05.030 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:05.032 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:02:05.032 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:05.034 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:05.039 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:02:05.042 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 配置使用记录更新，ID: 32, 使用次数: 66
2025-05-28 13:02:06.067 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:02:06.068 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:06.070 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:02:07.115 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:02:07.115 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:02:07.117 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=6, 题型配置={SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:10:48.107 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:10:48.107 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:10:48.111 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:10:48.111 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:10:48.113 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:10:48.118 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:10:49.825 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32, Token: exists
2025-05-28 13:10:49.825 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:10:49.827 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:10:49.827 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:10:49.829 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:10:49.834 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:10:50.894 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:10:50.894 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:10:50.896 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:10:51.948 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:10:51.948 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:10:51.950 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=6, 题型配置={SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:28:25.974 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-28 13:28:25.994 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-28 13:28:41.303 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on USER-20230226QO with PID 14788 (C:\Users\<USER>\IdeaProjects\maizi_edu_sys\target\classes started by Administrator in C:\Users\<USER>\IdeaProjects\maizi_edu_sys)
2025-05-28 13:28:41.308 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-05-28 13:28:41.309 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-05-28 13:28:46.649 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-05-28 13:28:46.650 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-28 13:28:46.650 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-28 13:28:46.950 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-28 13:28:48.216 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-28 13:28:48.704 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-28 13:28:50.099 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-05-28 13:28:50.100 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-05-28 13:28:50.100 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-05-28 13:28:53.440 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads, avatar=C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\avatars
2025-05-28 13:28:53.451 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-05-28 13:28:53.658 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-05-28 13:28:54.743 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-05-28 13:28:55.391 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback RedisTemplate. Redis operations will likely fail!
2025-05-28 13:28:55.934 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:C:\Users\<USER>\IdeaProjects\maizi_edu_sys\.\.\uploads\
2025-05-28 13:28:56.799 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-05-28 13:28:56.853 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 16.228 seconds (JVM running for 17.684)
2025-05-28 13:28:56.875 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 数据库中已存在paper表
2025-05-28 13:28:56.878 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表结构:
2025-05-28 13:28:56.878 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - id: bigint
2025-05-28 13:28:56.878 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - title: varchar
2025-05-28 13:28:56.878 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - user_id: bigint
2025-05-28 13:28:56.878 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_id: int
2025-05-28 13:28:56.879 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - knowledge_name: varchar
2025-05-28 13:28:56.879 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - total_score: int
2025-05-28 13:28:56.879 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - actual_total_score: int
2025-05-28 13:28:56.879 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty: double
2025-05-28 13:28:56.879 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - difficulty_distribution: json
2025-05-28 13:28:56.880 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - content: text
2025-05-28 13:28:56.880 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - config: text
2025-05-28 13:28:56.880 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - paper_type: varchar
2025-05-28 13:28:56.880 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - file_format: varchar
2025-05-28 13:28:56.881 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - download_count: int
2025-05-28 13:28:56.881 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - last_download_time: datetime
2025-05-28 13:28:56.881 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - create_time: datetime
2025-05-28 13:28:56.881 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - update_time: datetime
2025-05-28 13:28:56.881 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer -   - is_deleted: tinyint
2025-05-28 13:28:56.885 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - paper表示例数据: [{id=1, title= 识记类 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=1740, actual_total_score=null, difficulty=0.5, difficulty_distribution=[{"easy": 0.2, "hard": 0.1, "medium": 0.7}], content=1,2,3,4,5,6,7,8,9,10,11,12, config={"totalScore":72,"typeScoreMap":{"choice":5,"multiple":8,"judge":3,"fill":4,"short":10},"questionTypes":{"choice":3,"multiple":2,"judge":2,"fill":3,"short":2}}, paper_type=regular, file_format=pdf, download_count=28, last_download_time=2025-05-24T19:14:30, create_time=2025-05-16T21:40:57, update_time=2025-05-24T19:14:30, is_deleted=false}, {id=2, title= 识记类 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=1740, actual_total_score=null, difficulty=0.6999999999999966, difficulty_distribution=null, content=103797,103802,103813,103825,103826,103831,103834,103839,103856,103868,103893,103902,103905,103915,103921,103929,103936,103958,103965,103972,103984,103989,103997,104003,104016,104021,104029,104045,104050,104106,104115,104138,104156,104159,104188,104203,104207,104212,104215,104218,104224,104233,104234,104252,104262,104286,104890,104893,104897,104918,104925,104931,104943,104957,104962,104990,104998,105003,105021,105048,105051,105061,105071,105088,105102,105112,105116,105155,105161,105167,105171,105181,105190,105195,105207,105210,105216,105229,105234,105244,105248,105255,105257,105279,105285,105288,105296,105299,105306,105315,105317,106862,106864,106874,107004,107015,107050,107163,107165,107168,107173,107186,107222,107234,107264,107268,107401,107540,107542,107583,107587,107592,107611,107615,107618,107621,107622,107636,107643,107652,107657,107682,107684,107686,107690,107691,107693,107696,107700,107701,107709,107717,107725,107739,107744,107749,107753,107758,107761,107764,107777,107779,107781,107782,107786,107788,107790,107791,107792,107793,107794,107795,107801,107802,107804,107805,107808,107809,107810,107812,107814,107815,107816,107817,107824,107825,107829,107832,107834,107836,107850,107851,107852,107870,107872,107882,107888,107929,107942,107993,108012,108082,108131,108200,108364,108382,108384,108385,108389,108411,108417,108421,108440,108442,108445,108475,108509,108534,108569,108585,108590,108819,108837,108845,108879,108940,109022,109030,109034,109036,109050,109074,109082,109152,109220,109226,109227,109234,109314,109343,109417,109419,109543,109545,109579,109592,109649,109650,109697,109703,109704,109705,109708,109714,109718,109721,109731,109767,109769,109771,110009,110016,110168,110172,110175,110268,110292,110471,110489,110494,110502,110509,110525,110562,110576,110724,110731,110734,110738,110751,110756,110762,110777,110784,110791,110977,111018,111025,111032,111053,111061,111230,111249,111335,111350,111357,111449,111463,111465,111475,111485,111488,111489,111504,111514,111561,111563,111564,111566,111567,111571,111572,111603,111604,111619,111627,111628,111632,111633,111635,111638,111642,111648,111649,111655,111657,111663,111692,111694,111695,111697,111720,111731,111751,111757,111798,111822,111858,112003,112009,112029,112035,112044,112047,112192,112212,112303,112310,112321,112325,112329,112336,112349,112360,112420,112517,112524,112529,112530,112532,112554,112574,112595,112606,112613,112616,112621,112625,112627,112628,112635,112830,112831,113331,113347,113353,113368,113374,113381,113401,113419,113422,113430,113433,113439,113483,113523,113524,113544,113552,113554,113560,113563,113568,113572,113655,113747,113796,113802,113883,113925,113929,113935,113943,113951,113959,113975,113977,113981,113985,114010,114024,114030,114037,114119,114124,114135,114173,115006,115033,115056,115062,115064,115066,115177,115191,115202,115209,115219,115240,115248,115257,115303,115309,115336,115352,115366,115447,115567,115570,115620,115650,115668,115679,115688,115693,115707,115723,115730,115740,115744,115745,115753,115772,115806,115814,115820,115923,115927,115938,115947,115955,115968,116073,116076,116127,116131,116335,116340,116345,116442,116721,116722,116991,116996,116999,117004,117007,117014,117019,117027,117033,117199,117230,117239,117484,117491,117503,117507,117510,117672,117685,117693,117694,117777,117842,117845,117856,117977,117981,118120,118129,118185,118193,118195,118196,118197,118200,118201,118202,118242,118251,118254,119853,119856,119858,119862,119864,119865,123783,123802,123875,123986,131454,131455,131456,131492,131498,131501,131505,131507,131510,131513,131527,131531,131532,131533,131534,131535,131540,131541,131543,131547,131552,131555,131560,131562,131569,131573,131578,131584,131590,131592,131598,131602,131604,131606,131616,131617,131624,131628,131630,131637,131643,131645,131646,131647,131648,131652,131727,131728,131734,131737,131739,131749,131750,131754,131760,131762,131763,131768,131769,131785,131791,131795,131798,131806,131809,131814,131817,131818,131823,131824,131825,131827,131828,131830,131831,133520,160715, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:44:07, update_time=2025-05-16T21:44:07, is_deleted=false}, {id=3, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=99, actual_total_score=null, difficulty=0.5, difficulty_distribution=null, content=126658,126668,126680,126686,126714,126718,126873,126899,126949,126953,126957,126960,126971,126975,126976,126981,126985,126989,126996,126998,127009,127094,127124,127132,127139,127178,127193,127197,127310,127316,127467,127479,127534, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:53:09, update_time=2025-05-16T21:53:09, is_deleted=false}, {id=4, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=105, actual_total_score=null, difficulty=0.7, difficulty_distribution=null, content=126658,126663,126723,126818,126853,126882,126890,126950,126952,126953,126963,126964,126968,126975,126980,126981,126986,127001,127007,127011,127049,127069,127083,127138,127142,127151,127153,127155,127161,127170,127182,127313,127457,127473,127505, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:57:20, update_time=2025-05-16T21:57:20, is_deleted=true}, {id=5, title= 社会服务 能力测试, user_id=null, knowledge_id=null, knowledge_name=null, total_score=99, actual_total_score=null, difficulty=0.6928571428571428, difficulty_distribution=null, content=126641,126680,126718,126728,126762,126840,126845,126873,126890,126907,126914,126934,126959,126968,126975,126976,126984,126985,126991,127001,127011,127083,127111,127139,127148,127153,127157,127166,127174,127178,127501,127513,127532, config={"topicTypeCounts":{"singleChoice":10, "multipleChoice":5, "judgment":10, "shortAnswer":3}, "difficultyDistribution":{"easy":0.3, "medium":0.5, "hard":0.2}, "typeScoreMap":{"singleChoice":3, "multipleChoice":4, "judgment":2, "shortAnswer":10}}, paper_type=regular, file_format=pdf, download_count=0, last_download_time=null, create_time=2025-05-16T21:59:54, update_time=2025-05-16T21:59:54, is_deleted=false}]
2025-05-28 13:28:56.969 [http-nio-8081-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTc0ODM0NzQ1NywiZXhwIjoxNzQ4NDMzODU3fQ.Tyyp1nzT-Ad4D8rlJ8q8xc3gmoLzbFAOWSYoFohDDtn_ILyDzZeRD-x5Lqhr5aCJ9U4LCZrMSVCyFKmNpN6oSA] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-28 13:28:56.986 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-28 13:28:57.017 [main] INFO  com.edu.maizi_edu_sys.config.DataInitializer - 当前数据库中试卷数量: 136
2025-05-28 13:28:58.342 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 13:28:58.343 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:28:58.453 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 13:28:58.454 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...6oSA
2025-05-28 13:28:58.454 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:28:58.456 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:28:58.458 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 13:28:58.527 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 13:28:58.527 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:28:58.755 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 13:28:59.051 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-28 13:28:59.051 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:28:59.051 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-28 13:28:59.051 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:28:59.055 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-05-28 13:28:59.065 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-05-28 13:29:59.195 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:29:59.196 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:29:59.200 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:29:59.200 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:29:59.202 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:29:59.210 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:29:59.446 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserAndPublicConfigs(PaperConfigServiceImpl.java:199) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserAndPublicConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:30:00.138 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32, Token: exists
2025-05-28 13:30:00.139 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:00.142 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:30:00.142 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:00.144 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:00.151 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:30:00.189 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigById(PaperConfigServiceImpl.java:161) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigById(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfig(PaperConfigController.java:117) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:30:00.212 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32/use, Token: exists
2025-05-28 13:30:00.213 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:00.216 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:30:00.216 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:00.218 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:00.223 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:30:00.248 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 配置使用记录更新，ID: 32, 使用次数: 67
2025-05-28 13:30:01.229 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:30:01.229 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:01.233 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:30:02.696 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:30:02.696 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:02.716 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=6, 题型配置={SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:30:08.996 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 13:30:08.996 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:08.999 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 13:30:09.000 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...6oSA
2025-05-28 13:30:09.000 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:09.002 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:09.003 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 13:30:09.011 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 13:30:09.011 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:30:09.490 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 13:30:09.585 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-28 13:30:09.587 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:09.590 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-05-28 13:30:09.591 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-28 13:30:09.592 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:09.594 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-05-28 13:30:12.475 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/points, Token: exists
2025-05-28 13:30:12.475 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:12.494 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - API响应示例: id=20, knowledgeId=190, name= 识记类 
2025-05-28 13:30:12.495 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 原始Map数据: id=20, knowledgeId=190, name= 识记类 
2025-05-28 13:30:16.544 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:30:16.544 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:16.546 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:30:16.547 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:16.549 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:16.558 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:30:16.615 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserConfigs(PaperConfigServiceImpl.java:169) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:143) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:30:17.769 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32, Token: exists
2025-05-28 13:30:17.769 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:17.772 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:30:17.772 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:17.773 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:17.778 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:30:17.786 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigById(PaperConfigServiceImpl.java:161) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigById(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfig(PaperConfigController.java:117) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:30:17.799 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32/use, Token: exists
2025-05-28 13:30:17.799 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:17.801 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:30:17.801 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:17.803 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:17.808 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:30:17.811 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 配置使用记录更新，ID: 32, 使用次数: 68
2025-05-28 13:30:18.823 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:30:18.824 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:18.826 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:30:19.895 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:30:19.896 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:30:19.898 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=6, 题型配置={SINGLE_CHOICE=30, MULTIPLE_CHOICE=20, JUDGE=30, SHORT=3}
2025-05-28 13:33:17.781 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:33:17.781 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:17.784 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:33:17.784 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:17.785 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:17.788 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:33:17.792 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserAndPublicConfigs(PaperConfigServiceImpl.java:199) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserAndPublicConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:33:22.921 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 13:33:22.921 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:22.922 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/statistics, Token: exists
2025-05-28 13:33:22.922 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:22.923 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 13:33:22.923 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:33:22.923 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...6oSA
2025-05-28 13:33:22.923 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:22.923 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:22.924 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:33:22.924 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:22.925 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:22.925 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:22.925 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:33:22.925 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:22.927 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 13:33:22.928 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:22.932 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:33:22.932 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 13:33:22.933 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:33:22.934 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:33:22.940 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserConfigs(PaperConfigServiceImpl.java:169) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:143) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:33:22.949 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigStatistics(PaperConfigServiceImpl.java:804) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigStatistics(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfigStatistics(PaperConfigController.java:379) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:33:22.959 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  ,  ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigStatistics(PaperConfigServiceImpl.java:807) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigStatistics(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfigStatistics(PaperConfigController.java:379) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:33:25.916 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/32, Token: exists
2025-05-28 13:33:25.916 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:25.918 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:33:25.918 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:25.919 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:25.923 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:33:25.923 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 删除试卷配置，ID: 32
2025-05-28 13:33:25.932 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 配置删除成功，ID: 32
2025-05-28 13:33:25.941 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperConfigController - 用户 1920280447393230850 删除配置成功: 32
2025-05-28 13:33:25.956 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:33:25.956 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:25.958 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:33:25.958 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:25.959 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:25.965 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:33:28.976 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 13:33:28.976 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:28.977 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 13:33:28.977 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...6oSA
2025-05-28 13:33:28.977 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:28.978 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:28.979 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 13:33:28.984 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 13:33:28.984 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:33:29.141 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 13:33:29.505 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-28 13:33:29.505 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:29.505 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-28 13:33:29.505 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:29.507 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-05-28 13:33:29.508 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-05-28 13:33:30.120 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/points, Token: exists
2025-05-28 13:33:30.120 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:30.129 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - API响应示例: id=20, knowledgeId=190, name= 识记类 
2025-05-28 13:33:30.129 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 原始Map数据: id=20, knowledgeId=190, name= 识记类 
2025-05-28 13:33:42.485 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:33:42.485 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:42.487 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20}
2025-05-28 13:33:43.518 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:33:43.518 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:43.520 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20}
2025-05-28 13:33:46.686 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:33:46.686 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:46.688 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10}
2025-05-28 13:33:47.557 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:33:47.557 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:47.559 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10}
2025-05-28 13:33:48.838 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:33:48.838 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:48.842 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=30}
2025-05-28 13:33:51.549 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:33:51.549 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:51.551 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10}
2025-05-28 13:33:54.674 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:33:54.674 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:54.676 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:33:58.515 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:33:58.515 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:33:58.517 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:34:00.068 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:34:00.069 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:34:00.071 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:34:05.728 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:34:05.728 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:34:05.729 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:34:39.192 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:34:39.192 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:34:39.198 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:34:39.198 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:34:39.199 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:34:39.204 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:34:39.205 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 保存试卷配置: 识记类常用配置
2025-05-28 13:34:39.205 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 配置验证通过: 识记类常用配置
2025-05-28 13:34:39.228 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 配置保存成功，ID: 34
2025-05-28 13:34:39.233 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.saveConfig(PaperConfigServiceImpl.java:78) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.saveConfig(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.saveConfig(PaperConfigController.java:45) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 77 more
2025-05-28 13:34:39.245 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperConfigController - 用户 1920280447393230850 保存配置成功: 识记类常用配置
2025-05-28 13:35:33.763 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 13:35:33.763 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:33.767 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 13:35:33.767 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...6oSA
2025-05-28 13:35:33.767 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:33.770 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:33.771 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 13:35:33.776 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 13:35:33.776 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:35:34.131 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 13:35:34.333 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-28 13:35:34.333 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:34.333 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-28 13:35:34.334 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:34.339 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-05-28 13:35:34.339 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-05-28 13:35:34.834 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:35:34.834 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:34.837 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:35:34.837 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:34.838 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:34.843 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:35:34.850 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserAndPublicConfigs(PaperConfigServiceImpl.java:199) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserAndPublicConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:35:35.769 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/34, Token: exists
2025-05-28 13:35:35.770 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:35.771 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:35:35.771 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:35.772 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:35.777 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:35:35.786 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigById(PaperConfigServiceImpl.java:161) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigById(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfig(PaperConfigController.java:117) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:35:35.802 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/34/use, Token: exists
2025-05-28 13:35:35.802 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:35.805 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:35:35.805 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:35.806 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:35.812 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:35:35.816 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 配置使用记录更新，ID: 34, 使用次数: 1
2025-05-28 13:35:36.832 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:35:36.832 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:36.833 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:35:38.293 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:35:38.293 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:35:38.296 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:37:38.413 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-05-28 13:37:38.413 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:38.416 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-05-28 13:37:38.416 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...6oSA
2025-05-28 13:37:38.416 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:38.417 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:38.419 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-05-28 13:37:38.423 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-05-28 13:37:38.423 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-05-28 13:37:38.695 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-05-28 13:37:39.008 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-05-28 13:37:39.008 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:39.010 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-05-28 13:37:39.010 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers, Token: exists
2025-05-28 13:37:39.010 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:39.012 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-05-28 13:37:41.958 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:37:41.958 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:41.960 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:37:41.960 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:41.960 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:41.964 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:37:41.967 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserAndPublicConfigs(PaperConfigServiceImpl.java:199) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserAndPublicConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:37:42.548 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/34, Token: exists
2025-05-28 13:37:42.548 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:42.549 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:37:42.549 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:42.550 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:42.555 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:37:42.562 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigById(PaperConfigServiceImpl.java:161) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigById(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfig(PaperConfigController.java:117) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:37:42.573 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/34/use, Token: exists
2025-05-28 13:37:42.573 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:42.576 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:37:42.576 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:42.577 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:42.580 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:37:42.583 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 配置使用记录更新，ID: 34, 使用次数: 2
2025-05-28 13:37:43.638 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:37:43.638 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:43.640 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:37:45.113 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:37:45.113 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:37:45.114 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:43:05.489 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:43:05.489 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:05.493 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:43:05.493 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:05.494 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:05.498 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:43:05.502 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserAndPublicConfigs(PaperConfigServiceImpl.java:199) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserAndPublicConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:43:06.188 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/34, Token: exists
2025-05-28 13:43:06.188 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:06.189 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:43:06.189 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:06.192 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:06.195 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:43:06.210 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigById(PaperConfigServiceImpl.java:161) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigById(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfig(PaperConfigController.java:117) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:43:07.258 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:43:07.258 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:07.262 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:43:07.583 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:43:07.583 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:07.586 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:43:09.007 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:43:09.007 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:09.008 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:43:09.008 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:09.009 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:09.013 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:43:09.018 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserAndPublicConfigs(PaperConfigServiceImpl.java:199) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserAndPublicConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:43:20.087 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:43:20.087 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:20.089 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:43:20.089 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:20.090 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:20.093 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:43:20.097 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserAndPublicConfigs(PaperConfigServiceImpl.java:199) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserAndPublicConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:43:20.618 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/34, Token: exists
2025-05-28 13:43:20.618 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:20.619 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:43:20.619 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:20.620 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:20.624 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:43:20.629 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigById(PaperConfigServiceImpl.java:161) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigById(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfig(PaperConfigController.java:117) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:43:21.660 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:43:21.660 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:21.661 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:43:21.958 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:43:21.958 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:43:21.963 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:45:18.208 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:45:18.208 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:18.210 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:45:18.211 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:18.211 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:18.216 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:45:18.220 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserConfigs(PaperConfigServiceImpl.java:169) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:143) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:45:23.403 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:45:23.403 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:23.404 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:45:23.404 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:23.405 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:23.409 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:45:23.413 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserAndPublicConfigs(PaperConfigServiceImpl.java:199) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserAndPublicConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:45:24.086 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/34, Token: exists
2025-05-28 13:45:24.087 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:24.089 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:45:24.089 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:24.091 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:24.096 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:45:24.103 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigById(PaperConfigServiceImpl.java:161) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigById(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfig(PaperConfigController.java:117) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:45:25.147 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:45:25.147 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:25.151 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:45:25.460 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:45:25.460 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:45:25.463 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:46:02.888 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs, Token: exists
2025-05-28 13:46:02.888 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:02.890 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:46:02.890 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:02.891 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:02.894 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:46:02.898 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getUserAndPublicConfigs(PaperConfigServiceImpl.java:199) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getUserAndPublicConfigs(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getUserConfigs(PaperConfigController.java:141) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 78 more
2025-05-28 13:46:03.608 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/34, Token: exists
2025-05-28 13:46:03.608 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:03.610 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:46:03.610 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:03.611 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:03.616 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:46:03.621 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigById(PaperConfigServiceImpl.java:161) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigById(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfig(PaperConfigController.java:117) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:46:03.668 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/paper-configs/34, Token: exists
2025-05-28 13:46:03.668 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:03.669 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-05-28 13:46:03.669 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:03.670 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:03.672 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-05-28 13:46:03.677 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl - 填充知识点名称时出现错误: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/repository/KnowledgePointRepository.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT id,name,classification_id,create_time,update_time,is_deleted FROM knowledge_point WHERE id IN (   ?  ,  ?  )  AND is_deleted=false
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy153.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy176.selectBatchIds(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.enrichKnowledgePointConfigsWithNames(PaperConfigServiceImpl.java:771) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.convertToDTO(PaperConfigServiceImpl.java:392) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl.getConfigById(PaperConfigServiceImpl.java:161) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$FastClassBySpringCGLIB$$c4442212.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperConfigServiceImpl$$EnhancerBySpringCGLIB$$6c986d26.getConfigById(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperConfigController.getConfig(PaperConfigController.java:117) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.knowledge_point' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor155.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy199.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor154.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy198.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy197.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor158.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 71 more
2025-05-28 13:46:04.674 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:46:04.674 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:04.676 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:46:04.709 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/all, Token: exists
2025-05-28 13:46:04.709 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:04.711 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取知识点列表: search=null, filter=null, page=1, limit=1000
2025-05-28 13:46:05.033 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/papers/preview, Token: exists
2025-05-28 13:46:05.033 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-05-28 13:46:05.035 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=2, 题型配置={SINGLE_CHOICE=20, MULTIPLE_CHOICE=10, JUDGE=10, SHORT=2}
2025-05-28 13:54:24.443 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-28 13:54:24.454 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
