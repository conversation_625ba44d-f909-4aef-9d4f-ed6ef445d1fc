/**
 * 选项显示测试脚本
 * 用于验证单选题和多选题的选项是否正确显示
 */

$(document).ready(function() {
    console.log('🧪 选项显示测试脚本已加载');

    // 延迟执行测试，确保页面完全加载
    setTimeout(function() {
        runOptionsDisplayTests();
    }, 3000);
});

/**
 * 运行选项显示测试
 */
function runOptionsDisplayTests() {
    console.log('🔍 开始选项显示测试...');

    // 测试1: 检查选项容器
    testOptionsContainers();

    // 测试2: 检查选项数据
    testOptionsData();

    // 测试3: 检查选项渲染
    testOptionsRendering();

    // 测试4: 模拟选项解析
    testOptionsParsingLogic();

    console.log('✅ 选项显示测试完成');
}

/**
 * 测试选项容器
 */
function testOptionsContainers() {
    console.log('📦 测试选项容器...');

    const optionsContainers = $('.options');
    const topicOptionsContainers = $('.topic-options');
    const choiceQuestions = $('.question').filter(function() {
        const questionText = $(this).text().toLowerCase();
        return questionText.includes('选择') || questionText.includes('choice');
    });

    console.log(`- 选项容器数量: ${optionsContainers.length}`);
    console.log(`- 题目选项容器数量: ${topicOptionsContainers.length}`);
    console.log(`- 选择题数量: ${choiceQuestions.length}`);

    // 检查每个选项容器
    topicOptionsContainers.each(function(index) {
        const $container = $(this);
        const topicId = $container.attr('data-topic-id');
        const optionsData = $container.attr('data-options');
        const hasRenderedOptions = $container.find('.option').length > 0;

        console.log(`  题目 ${topicId}:`);
        console.log(`    - 有选项数据: ${!!optionsData}`);
        console.log(`    - 已渲染选项: ${hasRenderedOptions}`);
        console.log(`    - 选项数量: ${$container.find('.option').length}`);

        if (optionsData && !hasRenderedOptions) {
            console.warn(`    ⚠️ 题目 ${topicId} 有选项数据但未渲染`);
        }
    });
}

/**
 * 测试选项数据
 */
function testOptionsData() {
    console.log('📊 测试选项数据...');

    $('.topic-options').each(function() {
        const $container = $(this);
        const topicId = $container.attr('data-topic-id');
        const optionsData = $container.attr('data-options');

        if (optionsData) {
            console.log(`题目 ${topicId} 的原始选项数据:`, optionsData);

            try {
                const parsed = JSON.parse(optionsData);
                console.log(`题目 ${topicId} 的解析后选项:`, parsed);

                // 分析选项格式
                if (Array.isArray(parsed)) {
                    console.log(`  - 格式: 数组 (${parsed.length} 个选项)`);
                    parsed.forEach((option, idx) => {
                        if (typeof option === 'object') {
                            console.log(`    选项 ${idx}: {key: "${option.key}", name: "${option.name}"}`);
                        } else {
                            console.log(`    选项 ${idx}: "${option}"`);
                        }
                    });
                } else if (typeof parsed === 'object') {
                    console.log(`  - 格式: 对象 (${Object.keys(parsed).length} 个选项)`);
                    Object.keys(parsed).forEach(key => {
                        console.log(`    ${key}: "${parsed[key]}"`);
                    });
                } else {
                    console.warn(`  - 格式: 未知 (${typeof parsed})`);
                }
            } catch (e) {
                console.error(`题目 ${topicId} 选项解析失败:`, e);
            }
        }
    });
}

/**
 * 测试选项渲染
 */
function testOptionsRendering() {
    console.log('🎨 测试选项渲染...');

    const renderedOptions = $('.option');
    console.log(`已渲染的选项总数: ${renderedOptions.length}`);

    // 检查渲染质量
    renderedOptions.each(function(index) {
        const $option = $(this);
        const label = $option.find('.option-label').text();
        const text = $option.find('.option-text').text();

        if (!label || !text) {
            console.warn(`选项 ${index + 1} 渲染不完整: label="${label}", text="${text}"`);
        }
    });

    // 检查CSS样式
    const hasOptionStyles = $('.option').first().css('background-color') !== 'rgba(0, 0, 0, 0)';
    console.log(`选项样式已应用: ${hasOptionStyles}`);
}

/**
 * 测试选项解析逻辑
 */
function testOptionsParsingLogic() {
    console.log('🔧 测试选项解析逻辑...');

    // 测试不同格式的选项数据
    const testCases = [
        {
            name: '新格式数组',
            data: '[{"key":"A","name":"选项A"},{"key":"B","name":"选项B"}]'
        },
        {
            name: '旧格式对象',
            data: '{"A":"选项A","B":"选项B","C":"选项C"}'
        },
        {
            name: '简单字符串数组',
            data: '["选项A","选项B","选项C"]'
        },
        {
            name: '错误格式',
            data: 'invalid json'
        }
    ];

    testCases.forEach(testCase => {
        console.log(`测试 ${testCase.name}:`);
        try {
            const parsed = JSON.parse(testCase.data);
            console.log(`  解析成功:`, parsed);

            // 模拟渲染逻辑
            let optionsHtml = '';
            if (Array.isArray(parsed)) {
                parsed.forEach((option, idx) => {
                    let label, text;
                    if (typeof option === 'object' && option !== null) {
                        label = option.key || String.fromCharCode(65 + idx);
                        text = option.name || option.text || option.value || '';
                    } else {
                        label = String.fromCharCode(65 + idx);
                        text = option;
                    }
                    optionsHtml += `${label}. ${text}; `;
                });
            } else if (typeof parsed === 'object') {
                Object.keys(parsed).forEach(key => {
                    optionsHtml += `${key}. ${parsed[key]}; `;
                });
            }
            console.log(`  渲染结果: ${optionsHtml}`);
        } catch (e) {
            console.log(`  解析失败: ${e.message}`);
        }
    });
}

/**
 * 手动修复选项显示
 */
window.fixOptionsDisplay = function() {
    console.log('🔧 手动修复选项显示...');

    $('.topic-options').each(function() {
        const $container = $(this);
        const optionsData = $container.attr('data-options');
        const topicId = $container.attr('data-topic-id');

        if (optionsData && optionsData.trim() !== '') {
            try {
                const options = JSON.parse(optionsData);
                let optionsHtml = '';

                if (Array.isArray(options)) {
                    options.forEach((option, idx) => {
                        let label, text;
                        if (typeof option === 'object' && option !== null) {
                            label = option.key || String.fromCharCode(65 + idx);
                            text = option.name || option.text || option.value || '';
                        } else {
                            label = String.fromCharCode(65 + idx);
                            text = option;
                        }

                        optionsHtml += `
                            <div class="option">
                                <span class="option-label">${label}.</span>
                                <span class="option-text">${escapeHtml(text)}</span>
                            </div>
                        `;
                    });
                } else if (typeof options === 'object') {
                    Object.keys(options).forEach(key => {
                        optionsHtml += `
                            <div class="option">
                                <span class="option-label">${key}.</span>
                                <span class="option-text">${escapeHtml(options[key])}</span>
                            </div>
                        `;
                    });
                }

                if (optionsHtml) {
                    $container.html(optionsHtml);
                    console.log(`✅ 修复题目 ${topicId} 的选项显示`);
                }
            } catch (e) {
                console.error(`❌ 修复题目 ${topicId} 失败:`, e);
            }
        }
    });

    console.log('✅ 选项显示修复完成');
};

/**
 * 显示选项调试面板
 */
window.showOptionsDebugPanel = function() {
    const optionsCount = $('.option').length;
    const containersCount = $('.topic-options').length;
    const choiceQuestionsCount = $('.question').filter(function() {
        return $(this).find('.options').length > 0;
    }).length;

    const debugInfo = `
        <div style="font-family: monospace; font-size: 12px; text-align: left;">
            <h6>🔍 选项显示调试信息</h6>
            <p><strong>已渲染选项:</strong> ${optionsCount} 个</p>
            <p><strong>选项容器:</strong> ${containersCount} 个</p>
            <p><strong>选择题数量:</strong> ${choiceQuestionsCount} 个</p>
            <hr>
            <p><strong>快速修复:</strong></p>
            <button onclick="window.fixOptionsDisplay()" class="btn btn-sm btn-warning">修复选项显示</button>
            <button onclick="runOptionsDisplayTests()" class="btn btn-sm btn-info ml-2">重新测试</button>
        </div>
    `;

    Swal.fire({
        title: '选项调试面板',
        html: debugInfo,
        width: '500px',
        showConfirmButton: false,
        showCloseButton: true
    });
};

// HTML转义函数
function escapeHtml(text) {
    if (typeof text !== 'string') return text;
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// 在控制台提供快捷命令提示
console.log(`
🔍 选项显示调试命令:
- runOptionsDisplayTests() - 运行选项显示测试
- fixOptionsDisplay() - 修复选项显示
- showOptionsDebugPanel() - 显示调试面板
`);
