/**
 *  试卷配置集成 JavaScript
 * 功能：在试卷生成页面中集成配置的加载和保存功能
 */

// 全局变量
let availableConfigs = [];
let selectedConfigId = null;
let currentConfigData = null;

// 页面加载完成后初始化
$(document).ready(function() {
    initializeConfigIntegration();
    checkForConfigParameter();
});

/**
 *  初始化配置集成功能
 */
function initializeConfigIntegration() {
    // 防止重复初始化
    if (window.configIntegrationInitialized) {
        console.log('⚠️ 配置集成功能已经初始化，跳过重复初始化');
        return;
    }

    console.log('🔧 初始化配置集成功能...');

    // 绑定事件监听器（使用off先解绑，防止重复绑定）
    $('#loadConfigBtn').off('click').on('click', showLoadConfigModal);
    $('#saveConfigBtn').off('click').on('click', showSaveConfigModal);
    $('#loadSelectedConfigBtn').off('click').on('click', loadSelectedConfig);
    $('#saveConfigSubmitBtn').off('click').on('click', saveCurrentConfig);

    //  模态框内的配置管理按钮
    $('#loadConfigBtnModal').on('click', showLoadConfigModal);
    $('#saveConfigBtnModal').on('click', showSaveConfigModal);
    $('#manageConfigBtnModal').on('click', function() {
        window.open('/paper-configs', '_blank');
    });

    //  取消按钮事件
    $('#cancelSaveConfigBtn').on('click', function() {
        $('#saveConfigModal').modal('hide');
    });

    $('#cancelLoadConfigBtn').on('click', function() {
        $('#loadConfigModal').modal('hide');
    });

    //  清除选择按钮事件
    $('#clearSelectionBtn').on('click', function() {
        unselectAllConfigs();

        // 显示清除成功提示
        Swal.fire({
            icon: 'info',
            title: '已清除选择',
            text: '所有配置的选中状态已清除',
            timer: 1500,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    });

    //  导入导出功能
    $('#importConfigBtn').on('click', function() {
        $('#importConfigFile').click();
    });

    $('#importConfigFile').on('change', handleConfigFileImport);

    // 搜索功能
    $('#configSearchInput').on('input', debounce(filterConfigs, 300));
    $('#includePublicConfigsModal').on('change', loadConfigsForModal);

    // 监听知识点选择变化，启用保存按钮
    $(document).on('knowledgePointsChanged', function() {
        updateSaveButtonState();
    });

    // 监听试卷配置变化
    $(document).on('paperConfigChanged', function() {
        updateSaveButtonState();
    });

    //  监听表单字段变化，启用保存按钮
    $(document).on('change input', '#generatePaperForm input, #generatePaperForm select', function() {
        updateSaveButtonState();
    });

    // 标记已初始化
    window.configIntegrationInitialized = true;
    console.log('✅ 配置集成功能初始化完成');
}

/**
 *  检查URL参数中的配置ID
 */
function checkForConfigParameter() {
    const urlParams = new URLSearchParams(window.location.search);
    const configId = urlParams.get('configId');
    const mode = urlParams.get('mode');

    if (configId) {
        console.log('🔍 检测到配置ID参数:', configId, '模式:', mode);
        loadConfigById(configId, mode);
    }
}

/**
 *  显示加载配置模态框
 */
function showLoadConfigModal() {
    //  检查是否在试卷生成配置页面内
    if ($('#paperGenerationModal').hasClass('show')) {
        // 在配置页面内，直接显示加载模态框
        $('#loadConfigModal').modal('show');
        loadConfigsForModal();
    } else {
        // 在页面外，显示快速加载选择
        showQuickLoadConfigModal();
    }
}

/**
 *  显示快速加载配置模态框
 */
function showQuickLoadConfigModal() {
    // 获取用户的所有配置（包括公共配置）
    $.ajax({
        url: '/api/paper-configs?includePublic=true',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                showQuickConfigSelector(response.data);
            } else {
                // 没有配置，提示用户创建
                Swal.fire({
                    icon: 'info',
                    title: '暂无配置',
                    text: '您还没有保存任何配置，是否前往配置管理页面？',
                    showCancelButton: true,
                    confirmButtonText: '前往配置管理',
                    cancelButtonText: '取消'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = '/paper-configs';
                    }
                });
            }
        },
        error: function(xhr) {
            console.error('获取配置列表失败:', xhr);
            showError('获取配置列表失败，请稍后重试');
        }
    });
}

/**
 *  显示快速配置选择器
 */
function showQuickConfigSelector(configs) {
    // 存储所有配置用于搜索
    window.quickConfigsData = configs;

    const configListHtml = renderQuickConfigList(configs);

    Swal.fire({
        title: '快速加载配置',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="quickConfigSearch" placeholder="搜索配置名称或描述...">
                    </div>
                </div>
                <div id="quickConfigList" style="max-height: 350px; overflow-y: auto;">
                    ${configListHtml}
                </div>
                <div class="mt-3 pt-3 border-top d-flex justify-content-between">
                    <button class="btn btn-outline-secondary btn-sm" onclick="window.location.href='/paper-configs'">
                        <i class="fas fa-cog me-1"></i>管理所有配置
                    </button>
                    <small class="text-muted align-self-center">共 ${configs.length} 个配置</small>
                </div>
            </div>
        `,
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: '取消',
        width: '700px',
        didOpen: () => {
            // 绑定搜索功能
            const searchInput = document.getElementById('quickConfigSearch');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const filteredConfigs = window.quickConfigsData.filter(config =>
                    config.configName.toLowerCase().includes(searchTerm) ||
                    (config.description && config.description.toLowerCase().includes(searchTerm))
                );

                const configListElement = document.getElementById('quickConfigList');
                configListElement.innerHTML = renderQuickConfigList(filteredConfigs);

                // 重新绑定事件
                bindQuickConfigEvents();

                // 更新计数
                const countElement = document.querySelector('.text-muted.align-self-center');
                if (countElement) {
                    countElement.textContent = `共 ${filteredConfigs.length} 个配置`;
                }
            });

            // 绑定配置选择事件
            bindQuickConfigEvents();

            // 聚焦搜索框
            searchInput.focus();
        }
    });
}

/**
 *  渲染快速配置列表
 */
function renderQuickConfigList(configs) {
    if (configs.length === 0) {
        return `
            <div class="text-center py-4">
                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                <p class="text-muted">没有找到匹配的配置</p>
            </div>
        `;
    }

    return configs.map(config => {
        const lastUsed = config.lastUsedAt ? formatDate(config.lastUsedAt) : '未使用';
        const totalQuestions = (config.singleChoiceCount || 0) + (config.multipleChoiceCount || 0) +
                              (config.judgmentCount || 0) + (config.fillCount || 0) + (config.shortAnswerCount || 0);
        const totalScore = config.totalScore || 0;

        return `
            <div class="config-option" data-config-id="${config.id}" style="cursor: pointer; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; margin-bottom: 8px; transition: all 0.2s;">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <h6 class="mb-0 me-2">${escapeHtml(config.configName)}</h6>
                            ${config.isDefault ? '<span class="badge bg-primary">默认</span>' : ''}
                            ${config.isPublic ? '<span class="badge bg-info ms-1">公共</span>' : ''}
                        </div>
                        <p class="text-muted small mb-2">${escapeHtml(config.description || '无描述')}</p>
                        <div class="row text-center">
                            <div class="col-4">
                                <small class="text-muted">题数</small>
                                <div class="fw-bold small">${totalQuestions}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">总分</small>
                                <div class="fw-bold small">${totalScore}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">使用次数</small>
                                <div class="fw-bold small">${config.usageCount || 0}</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-end ms-3">
                        <small class="text-muted">最后使用</small>
                        <div class="small">${lastUsed}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

/**
 *  绑定快速配置选择事件
 */
function bindQuickConfigEvents() {
    document.querySelectorAll('.config-option').forEach(option => {
        option.addEventListener('click', function() {
            const configId = this.getAttribute('data-config-id');
            Swal.close();
            loadConfigById(configId, 'use');
        });

        // 悬停效果
        option.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
            this.style.borderColor = '#007bff';
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 2px 8px rgba(0,123,255,0.15)';
        });

        option.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.borderColor = '#e0e0e0';
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
}

/**
 *  显示保存配置模态框
 */
function showSaveConfigModal() {
    //  只在试卷生成配置页面内才有意义
    if ($('#paperGenerationModal').hasClass('show')) {
        // 在配置页面内，检查知识点选择
        if (!hasSelectedKnowledgePoints()) {
            Swal.fire({
                icon: 'warning',
                title: '无法保存配置',
                text: '请先选择知识点后再保存配置'
            });
            return;
        }

        // 显示完整的保存配置模态框
        showFullSaveConfigModal();
    } else {
        // 在页面外，提示用户先进入配置页面
        Swal.fire({
            icon: 'info',
            title: '保存配置',
            html: `
                <div class="text-center">
                    <i class="fas fa-info-circle fa-3x text-info mb-3"></i>
                    <p>要保存配置，请先进入试卷生成配置页面</p>
                    <p class="text-muted">选择知识点并配置题型后，即可保存配置</p>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '进入配置页面',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                // 打开试卷生成配置页面
                if (typeof openPaperGenerationModal === 'function') {
                    openPaperGenerationModal([], '新建试卷配置');
                }
            }
        });
    }
}





/**
 *  显示完整的保存配置模态框
 */
function showFullSaveConfigModal() {
    // 清空表单
    $('#saveConfigForm')[0].reset();

    //  检查是否为编辑模式
    const isEditMode = window.currentEditingConfigId;

    if (isEditMode && currentConfigData) {
        // 编辑模式：预填充现有配置信息，但提供智能的名称建议
        const originalName = currentConfigData.configName || '';

        //  为编辑模式生成建议的配置名称
        let suggestedName = originalName;
        if (originalName) {
            // 检查是否已经有版本号后缀
            const versionMatch = originalName.match(/^(.+?)(?:_v(\d+))?$/);
            if (versionMatch) {
                const baseName = versionMatch[1];
                const currentVersion = versionMatch[2] ? parseInt(versionMatch[2]) : 1;
                suggestedName = `${baseName}_v${currentVersion + 1}`;
            } else {
                suggestedName = `${originalName}_v2`;
            }
        }

        $('#configName').val(suggestedName);
        $('#configDescription').val(currentConfigData.description || '');
        $('#titleTemplate').val(currentConfigData.titleTemplate || '');
        $('#setAsDefault').prop('checked', currentConfigData.isDefault || false);
        $('#makePublic').prop('checked', currentConfigData.isPublic || false);

        // 修改模态框标题和说明
        $('#saveConfigModalLabel').html('<i class="fas fa-edit me-2"></i>更新试卷配置');
        $('#saveConfigSubmitBtn').html('<i class="fas fa-save me-1"></i>更新配置');

        // 添加编辑模式说明
        const editHint = `
            <div class="alert alert-info mt-2" id="editModeHint">
                <i class="fas fa-info-circle me-2"></i>
                <strong>编辑模式说明：</strong>
                <ul class="mb-0 mt-1">
                    <li><strong>保持原名称</strong>：将覆盖原配置 "<em>${escapeHtml(originalName)}</em>"</li>
                    <li><strong>修改名称</strong>：将创建新配置，保留原配置</li>
                    <li><strong>建议名称</strong>：已自动生成版本号避免重名</li>
                </ul>
            </div>
        `;

        // 在配置名称输入框后添加说明
        if ($('#editModeHint').length === 0) {
            $('#configName').closest('.form-group').append(editHint);
        }

        // 监听配置名称变化，动态更新提示
        $('#configName').off('input.editMode').on('input.editMode', function() {
            const currentName = $(this).val().trim();
            const $hint = $('#editModeHint');

            if (currentName === originalName) {
                $hint.removeClass('alert-warning').addClass('alert-info');
                $hint.find('strong').first().text('编辑模式说明：');
                $hint.find('li').first().html(`<strong>当前将覆盖原配置</strong> "<em>${escapeHtml(originalName)}</em>"`);
            } else if (currentName) {
                $hint.removeClass('alert-info').addClass('alert-warning');
                $hint.find('strong').first().text('新配置模式：');
                $hint.find('li').first().html(`<strong>将创建新配置</strong> "<em>${escapeHtml(currentName)}</em>"，保留原配置`);
            }
        });

    } else {
        // 新建模式：生成默认值
        const defaultName = generateDefaultConfigName();
        $('#configName').val(defaultName);

        const defaultTemplate = generateDefaultTitleTemplate();
        $('#titleTemplate').val(defaultTemplate);

        // 恢复模态框标题
        $('#saveConfigModalLabel').html('<i class="fas fa-save me-2"></i>保存试卷配置');
        $('#saveConfigSubmitBtn').html('<i class="fas fa-save me-1"></i>保存配置');

        // 清理编辑模式提示
        $('#editModeHint').remove();
        $('#configName').off('input.editMode');
    }

    $('#saveConfigModal').modal('show');

    //  监听模态框关闭事件，清理编辑模式提示
    $('#saveConfigModal').off('hidden.bs.modal.editMode').on('hidden.bs.modal.editMode', function() {
        $('#editModeHint').remove();
        $('#configName').off('input.editMode');
    });
}

/**
 *  为模态框加载配置列表
 */
function loadConfigsForModal() {
    $('#configListLoading').show();
    $('#configList').hide();
    $('#configListEmpty').hide();

    const includePublic = $('#includePublicConfigsModal').is(':checked');
    const url = `/api/paper-configs?includePublic=${includePublic}`;

    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            $('#configListLoading').hide();
            if (response.success && response.data.length > 0) {
                availableConfigs = response.data;
                renderConfigsInModal(availableConfigs);
                $('#configList').show();
            } else {
                $('#configListEmpty').show();
            }
        },
        error: function(xhr) {
            $('#configListLoading').hide();
            $('#configListEmpty').show();
            console.error('加载配置列表失败:', xhr);
            showError('加载配置列表失败，请稍后重试');
        }
    });
}

/**
 *  在模态框中渲染配置列表
 */
function renderConfigsInModal(configs) {
    const container = $('#configList');

    if (configs.length === 0) {
        container.hide();
        $('#configListEmpty').show();
        return;
    }

    const configsHtml = configs.map(config => createConfigCardForModal(config)).join('');
    container.html(configsHtml);

    // 绑定选择事件
    $('.config-card-modal').on('click', function(e) {
        // 阻止事件冒泡，避免点击导出按钮时触发选择
        if ($(e.target).closest('button').length > 0) {
            return;
        }

        const $clickedCard = $(this);
        const configId = $clickedCard.data('config-id');

        // 检查是否已经选中了这个配置
        if ($clickedCard.hasClass('selected')) {
            // 如果已选中，则取消选择
            unselectAllConfigs();
            console.log('🔧 取消选中配置:', configId);
        } else {
            // 如果未选中，则选择这个配置
            selectConfig($clickedCard, configId);
            console.log('🔧 选中配置:', configId);
        }
    });
}

/**
 *  选择配置
 */
function selectConfig($card, configId) {
    // 移除所有选中状态
    unselectAllConfigs();

    // 添加选中状态到当前卡片
    $card.addClass('selected');

    // 显示选中状态提示
    $card.find('.selection-hint').show();

    // 更新选中的配置ID
    selectedConfigId = configId;

    // 启用加载按钮并添加动画效果
    const loadBtn = $('#loadSelectedConfigBtn');
    loadBtn.prop('disabled', false);

    // 显示清除选择按钮
    $('#clearSelectionBtn').show();

    // 按钮动画效果
    loadBtn.addClass('btn-pulse');
    setTimeout(() => {
        loadBtn.removeClass('btn-pulse');
    }, 600);

    // 可选：添加轻微的震动效果（如果支持）
    if (navigator.vibrate) {
        navigator.vibrate(50);
    }

    // 滚动到选中的卡片（如果不在视野内）
    $card[0].scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
    });
}

/**
 *  取消选择所有配置
 */
function unselectAllConfigs() {
    // 移除所有选中状态
    $('.config-card-modal').removeClass('selected');
    $('.selection-hint').hide();

    // 清空选中的配置ID
    selectedConfigId = null;

    // 禁用加载按钮
    const loadBtn = $('#loadSelectedConfigBtn');
    loadBtn.prop('disabled', true);

    // 隐藏清除选择按钮
    $('#clearSelectionBtn').hide();

    // 移除按钮动画
    loadBtn.removeClass('btn-pulse');

    console.log('🔧 已清除所有选中状态');
}

/**
 *  创建模态框中的配置卡片
 */
function createConfigCardForModal(config) {
    const totalQuestions = config.totalQuestions || 0;
    const totalScore = config.totalScore || 0;
    const lastUsed = config.lastUsedAt ? formatDate(config.lastUsedAt) : '从未使用';

    let badges = '';
    if (config.isDefault) {
        badges += '<span class="badge bg-warning me-1">默认</span>';
    }
    if (config.isPublic) {
        badges += '<span class="badge bg-info me-1">公共</span>';
    }

    return `
        <div class="col-md-6 mb-3">
            <div class="config-card-modal card h-100" data-config-id="${config.id}" style="cursor: pointer;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0">${escapeHtml(config.configName)}</h6>
                        <div class="d-flex align-items-center">
                            ${badges}
                            <button class="btn btn-sm btn-outline-secondary ms-1" onclick="exportSingleConfig(${config.id})" title="导出配置">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <p class="card-text small text-muted mb-2">${escapeHtml(config.description || '暂无描述')}</p>
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted">题数</small>
                            <div class="fw-bold">${totalQuestions}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">总分</small>
                            <div class="fw-bold">${totalScore}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">使用</small>
                            <div class="fw-bold">${config.usageCount || 0}</div>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>${lastUsed}
                        </small>
                    </div>
                    <!-- 选中状态提示 -->
                    <div class="selection-hint mt-2" style="display: none;">
                        <small class="text-primary fw-bold">
                            <i class="fas fa-check-circle me-1"></i>
                            已选中此配置
                        </small>
                        <small class="text-muted d-block mt-1">
                            <i class="fas fa-info-circle me-1"></i>
                            再次点击可取消选择
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 *  筛选配置
 */
function filterConfigs() {
    const searchTerm = $('#configSearchInput').val().toLowerCase();
    let filteredConfigs = availableConfigs;

    if (searchTerm) {
        filteredConfigs = availableConfigs.filter(config =>
            config.configName.toLowerCase().includes(searchTerm) ||
            (config.description && config.description.toLowerCase().includes(searchTerm))
        );
    }

    renderConfigsInModal(filteredConfigs);
}

/**
 *  加载选中的配置
 */
function loadSelectedConfig() {
    if (!selectedConfigId) {
        showError('请先选择一个配置');
        return;
    }

    loadConfigById(selectedConfigId);
    $('#loadConfigModal').modal('hide');
}

/**
 * 用于调试全局AJAX请求
 */
$(document).ajaxSend(function(event, jqxhr, settings) {
    console.log('🔍 AJAX请求发送:', {
        url: settings.url,
        type: settings.type,
        data: settings.data
    });
});

$(document).ajaxError(function(event, jqxhr, settings, thrownError) {
    console.error('❌ AJAX请求失败:', {
        url: settings.url,
        type: settings.type,
        status: jqxhr.status,
        statusText: jqxhr.statusText,
        responseText: jqxhr.responseText
    });
});

/**
 *  根据ID加载配置
 */
function loadConfigById(configId, mode = 'use') {
    if (!configId) {
        showError('配置ID不能为空');
        return;
    }

    // 防止重复加载同一个配置
    const loadKey = `${configId}-${mode}`;
    if (window.currentLoadingConfig === loadKey) {
        console.log('⚠️ 配置正在加载中，跳过重复请求:', loadKey);
        return;
    }

    window.currentLoadingConfig = loadKey;

    const isEditMode = mode === 'edit';
    const loadingTitle = isEditMode ? '加载配置进行编辑' : '加载配置';
    const loadingMessage = isEditMode ? '正在加载配置进行编辑...' : '正在加载配置...';

    showLoading(loadingMessage, loadingTitle);

    $.ajax({
        url: `/api/paper-configs/${configId}`,
        method: 'GET',
        success: function(response) {
            window.currentLoadingConfig = null; // 清除加载状态

            if (response.success) {
                currentConfigData = response.data;

                // 记录使用（编辑模式不记录使用）
                if (!isEditMode) {
                    recordConfigUsage(configId);
                }

                //  加载完配置后自动打开试卷生成模态框（自由组卷界面）
                setTimeout(function() {
                    // 确保加载模态框已经完全关闭
                    hideLoading();

                    console.log('🔍 检查 openPaperGenerationModal 函数:', typeof openPaperGenerationModal);

                    if (typeof openPaperGenerationModal === 'function') {
                        const config = response.data;
                        const knowledgeConfigs = config.knowledgePointConfigs || [];
                        const paperTitle = config.titleTemplate || '加载的配置试卷';

                        console.log(' 准备打开试卷生成模态框:', {
                            knowledgeConfigs: knowledgeConfigs,
                            paperTitle: paperTitle,
                            configName: config.configName
                        });

                        // 延迟一点时间确保加载模态框完全关闭
                        setTimeout(function() {
                            try {
                                console.log('🚀 正在调用 openPaperGenerationModal...');
                                openPaperGenerationModal(knowledgeConfigs, paperTitle);
                                console.log('✅ openPaperGenerationModal 调用成功');

                                //  模态框打开后，再应用配置参数
                                setTimeout(function() {
                                    console.log('🔧 模态框已打开，开始应用配置参数...');
                                    applyConfigToPage(config);

                                    // 延迟显示成功消息，确保配置已经应用
                                    setTimeout(function() {
                                        if (isEditMode) {
                                            // 编辑模式：显示特殊的编辑提示
                                            Swal.fire({
                                                icon: 'success',
                                                title: '编辑模式已激活',
                                                html: `
                                                    <div class="text-center">
                                                        <p>配置 "<strong>${escapeHtml(config.configName)}</strong>" 已加载</p>
                                                        <div class="alert alert-info mt-3">
                                                            <i class="fas fa-info-circle me-2"></i>
                                                            <strong>编辑模式说明：</strong>
                                                            <ul class="text-start mt-2 mb-0">
                                                                <li>您可以修改任何配置参数</li>
                                                                <li>修改完成后点击"保存配置"按钮</li>
                                                                <li>保存时会<strong>覆盖原配置</strong></li>
                                                                <li>如需保留原配置，请修改配置名称</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                `,
                                                confirmButtonText: '开始编辑',
                                                confirmButtonColor: '#28a745'
                                            });
                                        } else {
                                            showSuccess('配置加载成功，已进入试卷生成配置界面');
                                        }
                                    }, 300);
                                }, 500); // 等待模态框完全打开

                                // 如果是编辑模式，设置当前编辑的配置ID
                                if (isEditMode) {
                                    window.currentEditingConfigId = configId;
                                    // 修改页面标题提示用户正在编辑
                                    document.title = `编辑配置: ${config.configName} - 麦子教育系统`;
                                }
                            } catch (error) {
                                console.error('❌ 调用 openPaperGenerationModal 失败:', error);
                                showError('打开试卷生成配置页面失败');
                            }
                        }, 300);
                    } else {
                        console.warn('⚠️ openPaperGenerationModal 函数不存在');

                        // 如果函数不存在，提供手动打开的选项
                        const config = response.data;

                        Swal.fire({
                            icon: 'warning',
                            title: '需要手动操作',
                            html: `
                                <div class="text-center">
                                    <p>配置 "<strong>${escapeHtml(config.configName)}</strong>" 已加载</p>
                                    <p class="text-muted">请点击下方按钮打开试卷生成配置页面</p>
                                    ${isEditMode ? '<p class="text-warning"><i class="fas fa-edit me-1"></i>编辑模式：修改完成后请保存配置</p>' : ''}
                                </div>
                            `,
                            showCancelButton: true,
                            confirmButtonText: '打开配置页面',
                            cancelButtonText: '取消',
                            confirmButtonColor: '#007bff'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // 尝试手动触发自由组卷按钮
                                const generateBtn = $('#generatePaperBtn, #generatePaperBtnBottom').first();
                                if (generateBtn.length > 0) {
                                    generateBtn.click();
                                } else {
                                    // 如果按钮不存在，提示用户手动操作
                                    showError('请手动点击"自由组卷"按钮开始配置试卷');
                                }
                            }
                        });

                        // 应用配置到页面
                        applyConfigToPage(response.data);

                        // 如果是编辑模式，设置编辑状态
                        if (isEditMode) {
                            window.currentEditingConfigId = configId;
                            document.title = `编辑配置: ${config.configName} - 麦子教育系统`;
                        }
                    }
                }, 200); // 减少延迟时间
            } else {
                // 加载失败时也要确保模态框关闭
                hideLoading();
                setTimeout(function() {
                    showError('加载配置失败: ' + response.message);
                }, 100);
            }
        },
        error: function(xhr) {
            window.currentLoadingConfig = null; // 清除加载状态

            // 确保加载模态框关闭
            hideLoading();

            // 延迟显示错误信息，确保模态框完全关闭
            setTimeout(function() {
                showError('加载配置失败，请稍后重试');
            }, 100);

            console.error('加载配置失败:', xhr);
        }
    });
}

/**
 *  将配置应用到页面
 */
function applyConfigToPage(config) {
    console.log('🔧 开始应用配置到页面:', config);

    try {
        // 1. 应用知识点配置
        if (config.knowledgePointConfigs && config.knowledgePointConfigs.length > 0) {
            console.log('🔧 应用知识点配置:', config.knowledgePointConfigs);
            applyKnowledgePointConfigs(config.knowledgePointConfigs);
        }

        // 2.  应用题型配置到模态框
        console.log('🔧 应用题型配置...');
        console.log('题型配置详情:', {
            singleChoice: { count: config.singleChoiceCount, score: config.singleChoiceScore },
            multipleChoice: { count: config.multipleChoiceCount, score: config.multipleChoiceScore },
            judgment: { count: config.judgmentCount, score: config.judgmentScore },
            fill: { count: config.fillCount, score: config.fillScore },
            shortAnswer: { count: config.shortAnswerCount, score: config.shortAnswerScore }
        });

        // 应用到普通生成模态框
        $('#singleChoiceCount').val(config.singleChoiceCount || 0);
        $('#singleChoiceScore').val(config.singleChoiceScore || 0);
        $('#multipleChoiceCount').val(config.multipleChoiceCount || 0);
        $('#multipleChoiceScore').val(config.multipleChoiceScore || 0);
        $('#judgmentCount').val(config.judgmentCount || 0);
        $('#judgmentScore').val(config.judgmentScore || 0);
        $('#fillCount').val(config.fillCount || 0);
        $('#fillScore').val(config.fillScore || 0);
        $('#shortAnswerCount').val(config.shortAnswerCount || 0);
        $('#shortAnswerScore').val(config.shortAnswerScore || 0);

        // 应用到自由组卷模态框
        $('#customSingleChoiceCount').val(config.singleChoiceCount || 0);
        $('#customSingleChoiceScore').val(config.singleChoiceScore || 0);
        $('#customMultipleChoiceCount').val(config.multipleChoiceCount || 0);
        $('#customMultipleChoiceScore').val(config.multipleChoiceScore || 0);
        $('#customJudgmentCount').val(config.judgmentCount || 0);
        $('#customJudgmentScore').val(config.judgmentScore || 0);
        $('#customFillCount').val(config.fillCount || 0);
        $('#customFillScore').val(config.fillScore || 0);
        $('#customShortAnswerCount').val(config.shortAnswerCount || 0);
        $('#customShortAnswerScore').val(config.shortAnswerScore || 0);
        $('#customSubjectiveCount').val(config.subjectiveCount || 0);
        $('#customSubjectiveScore').val(config.subjectiveScore || 0);

        console.log('✅ 已应用题型配置到普通和自由组卷模态框');

        // 验证题型配置是否正确应用
        console.log('🔍 验证题型配置应用结果:');
        console.log('单选题数量:', $('#singleChoiceCount').val(), '分值:', $('#singleChoiceScore').val());
        console.log('多选题数量:', $('#multipleChoiceCount').val(), '分值:', $('#multipleChoiceScore').val());
        console.log('判断题数量:', $('#judgmentCount').val(), '分值:', $('#judgmentScore').val());
        console.log('填空题数量:', $('#fillCount').val(), '分值:', $('#fillScore').val());
        console.log('简答题数量:', $('#shortAnswerCount').val(), '分值:', $('#shortAnswerScore').val());

        // 3.  应用试卷基本信息
        console.log('🔧 应用试卷基本信息...');
        if (config.titleTemplate) {
            $('#paperTitle').val(config.titleTemplate);
            $('#customPaperTitle').val(config.titleTemplate);
            console.log('试卷标题:', config.titleTemplate);
        }

        //  如果配置中没有指定试卷版本，默认使用学生版
        const paperType = config.paperType || 'regular';
        $('#paperType').val(paperType);
        $('#customPaperType').val(paperType);
        console.log('试卷类型:', paperType);

        // 触发版本选择变化事件，更新描述
        if (typeof updateVersionDescription === 'function') {
            const descriptionElement = $('#paperType').closest('.form-group').find('[id$="Description"]');
            updateVersionDescription(paperType, descriptionElement);

            const customDescriptionElement = $('#customPaperType').closest('.form-group').find('[id$="Description"]');
            updateVersionDescription(paperType, customDescriptionElement);
        }

        if (config.paperCount) {
            $('#paperCount').val(config.paperCount);
            console.log('生成套数:', config.paperCount);
        }

        // 4.  应用难度分布配置
        console.log('🔧 应用难度分布配置...');
        if (config.difficultyDistribution) {
            const easyPercent = (config.difficultyDistribution.easy * 100) || 30;
            const mediumPercent = (config.difficultyDistribution.medium * 100) || 50;
            const hardPercent = (config.difficultyDistribution.hard * 100) || 20;

            $('#easyPercentage').val(easyPercent);
            $('#mediumPercentage').val(mediumPercent);
            $('#hardPercentage').val(hardPercent);

            console.log('难度分布:', { easy: easyPercent + '%', medium: mediumPercent + '%', hard: hardPercent + '%' });
        }

        // 5.  触发页面更新事件
        console.log('🔧 触发页面更新事件...');
        setTimeout(function() {
            // 触发表单变化事件，更新统计信息
            $('#singleChoiceCount, #singleChoiceScore, #multipleChoiceCount, #multipleChoiceScore').trigger('change');
            $('#judgmentCount, #judgmentScore, #fillCount, #fillScore, #shortAnswerCount, #shortAnswerScore').trigger('change');
            $('#easyPercentage, #mediumPercentage, #hardPercentage').trigger('change');

            // 触发自由组卷界面的表单变化事件
            $('#customSingleChoiceCount, #customSingleChoiceScore, #customMultipleChoiceCount, #customMultipleChoiceScore').trigger('change');
            $('#customJudgmentCount, #customJudgmentScore, #customFillCount, #customFillScore').trigger('change');
            $('#customShortAnswerCount, #customShortAnswerScore, #customSubjectiveCount, #customSubjectiveScore').trigger('change');

            // 更新总分和题目数统计
            if (typeof calculateTotalScore === 'function') {
                calculateTotalScore();
                console.log('✅ 总分计算完成');
            }

            // 更新自由组卷界面的总分
            updateCustomPaperTotalScore();

            // 更新图表
            if (typeof updateDifficultyChart === 'function') {
                updateDifficultyChart();
                console.log('✅ 难度分布图表更新完成');
            }
            if (typeof updateQuestionDistributionChart === 'function') {
                updateQuestionDistributionChart();
                console.log('✅ 题型分布图表更新完成');
            }

            // 更新预览
            if (typeof updatePaperPreview === 'function') {
                updatePaperPreview();
                console.log('✅ 试卷预览更新完成');
            }

            // 验证最终应用结果
            console.log('🔍 最终验证 - 当前表单值:');
            console.log('单选题:', $('#singleChoiceCount').val(), '×', $('#singleChoiceScore').val(), '分');
            console.log('多选题:', $('#multipleChoiceCount').val(), '×', $('#multipleChoiceScore').val(), '分');
            console.log('判断题:', $('#judgmentCount').val(), '×', $('#judgmentScore').val(), '分');
            console.log('填空题:', $('#fillCount').val(), '×', $('#fillScore').val(), '分');
            console.log('简答题:', $('#shortAnswerCount').val(), '×', $('#shortAnswerScore').val(), '分');
            console.log('难度分布:', $('#easyPercentage').val() + '%', $('#mediumPercentage').val() + '%', $('#hardPercentage').val() + '%');
        }, 200);

        // 6. 更新保存按钮状态
        updateSaveButtonState();

        console.log('✅ 配置应用完成');

    } catch (error) {
        console.error('❌ 应用配置失败:', error);
        showError('应用配置时发生错误: ' + error.message);
    }
}

/**
 * 🔥 应用知识点配置
 */
function applyKnowledgePointConfigs(knowledgePointConfigs) {
    console.log('🔧 开始应用知识点配置:', knowledgePointConfigs);

    if (!knowledgePointConfigs || knowledgePointConfigs.length === 0) {
        console.warn('⚠️ 没有知识点配置需要应用');
        return;
    }

    // 1. 清空当前选择状态
    console.log('🔧 清空当前知识点选择状态...');
    if (typeof window.selectedKnowledgePoints !== 'undefined') {
        window.selectedKnowledgePoints.clear();
    }

    // 清空已选择知识点显示区域
    $('#selected-knowledge-points-container').empty();
    $('.selectedCount').text('0');

    // 2. 清空自由组卷知识点列表
    if (typeof customSelectedKnowledgePoints !== 'undefined') {
        customSelectedKnowledgePoints = [];
        $('#knowledgePointsContainer').empty();
    }

    // 3. 清空知识点信息映射
    if (typeof window.knowledgeInfo !== 'undefined') {
        window.knowledgeInfo = {};
    }

    // 提取所有知识点ID，用于后续获取名称
    const knowledgeIds = knowledgePointConfigs.map(config => config.knowledgeId || config.knowId).filter(id => id);
    console.log('🔍 需要获取信息的知识点ID:', knowledgeIds);

    // 检查哪些知识点缺少名称
    const missingNameIds = [];
    knowledgePointConfigs.forEach(config => {
        const id = config.knowledgeId || config.knowId;
        // 如果没有名称或名称是默认格式，认为需要获取
        if (!config.knowledgeName || config.knowledgeName.includes('知识点') || config.knowledgeName === `知识点${id}`) {
            missingNameIds.push(id);
        }
    });

    console.log('🔍 检查知识点名称状态:', {
        totalConfigs: knowledgePointConfigs.length,
        missingNameIds: missingNameIds,
        configDetails: knowledgePointConfigs.map(c => ({
            id: c.knowledgeId || c.knowId,
            name: c.knowledgeName,
            needsName: !c.knowledgeName || c.knowledgeName.includes('知识点')
        }))
    });

    // 如果有缺失名称的知识点，尝试获取
    if (missingNameIds.length > 0) {
        console.log('🔍 尝试获取缺失的知识点名称:', missingNameIds);

        // 防止重复请求
        if (window.knowledgeApiRequesting) {
            console.log('⚠️ 知识点API请求正在进行中，跳过重复请求');
            processKnowledgePoints(knowledgePointConfigs);
            return;
        }

        window.knowledgeApiRequesting = true;

        // 使用现有的知识点API获取所有知识点信息
        $.ajax({
            url: '/api/knowledge/all',
            method: 'GET',
            data: {
                limit: 1000  // 获取足够多的知识点
            },
            success: function(response) {
                window.knowledgeApiRequesting = false;

                if (response && response.success && response.data) {
                    console.log('✅ 获取知识点信息成功:', response.data.length);

                    // 创建ID到知识点信息的映射
                    const knowledgeMap = {};
                    response.data.forEach(point => {
                        // 兼容不同的数据结构
                        const id = point.knowledgeId || point.id;
                        const name = point.knowledgeName || point.name;
                        if (id) {
                            knowledgeMap[id] = {
                                id: id,
                                name: name,
                                knowledgeName: name,
                                groupName: point.groupName,
                                topicCount: point.topicCount
                            };
                        }
                    });

                    // 更新知识点配置
                    let updatedCount = 0;
                    knowledgePointConfigs.forEach(config => {
                        const id = config.knowledgeId || config.knowId;
                        if (knowledgeMap[id]) {
                            const oldName = config.knowledgeName;
                            config.knowledgeName = knowledgeMap[id].name || config.knowledgeName;
                            config.groupName = knowledgeMap[id].groupName || config.groupName;
                            config.topicCount = knowledgeMap[id].topicCount || config.topicCount;

                            if (oldName !== config.knowledgeName) {
                                updatedCount++;
                                console.log(`✅ 更新知识点名称: ID=${id}, ${oldName} → ${config.knowledgeName}`);
                            }
                        } else {
                            console.warn(`⚠️ 未找到知识点信息: ID=${id}`);
                        }
                    });

                    console.log(`✅ 知识点名称更新完成: ${updatedCount}/${knowledgePointConfigs.length} 个知识点名称已更新`);
                }

                // 处理知识点（无论API是否成功）
                processKnowledgePoints(knowledgePointConfigs);
            },
            error: function(xhr, status, error) {
                window.knowledgeApiRequesting = false;
                console.error('❌ 获取知识点信息失败:', {xhr, status, error});
                // 出错时仍然使用已有信息继续
                console.log('⚠️ 使用已有信息继续处理知识点配置');
                processKnowledgePoints(knowledgePointConfigs);
            }
        });
    } else {
        // 没有缺失名称的知识点，直接处理
        processKnowledgePoints(knowledgePointConfigs);
    }
}

/**
 * 处理知识点配置并添加到全局状态
 */
function processKnowledgePoints(knowledgePointConfigs) {
    console.log('🔧 处理知识点配置...');

    knowledgePointConfigs.forEach((kpConfig, index) => {
        // 从配置中获取知识点信息
        const knowledgeId = kpConfig.knowledgeId || kpConfig.knowId;
        const knowledgeName = kpConfig.knowledgeName || `知识点${knowledgeId}`;
        const questionCount = kpConfig.questionCount || 0;

        console.log(`🔧 处理知识点配置 ${index + 1}/${knowledgePointConfigs.length}:`, {
            knowledgeId: knowledgeId,
            knowledgeName: knowledgeName,
            questionCount: questionCount,
            includeShortAnswer: kpConfig.includeShortAnswer,
            shortAnswerCount: kpConfig.shortAnswerCount,
            originalConfig: kpConfig
        });

        if (!knowledgeId) {
            console.warn('⚠️ 知识点配置缺少ID:', kpConfig);
            return;
        }

        // 添加到全局选择状态
        if (typeof window.selectedKnowledgePoints !== 'undefined') {
            window.selectedKnowledgePoints.set(knowledgeId.toString(), {
                id: knowledgeId.toString(),
                name: knowledgeName,
                topicCount: (kpConfig.topicCount || 0) + '题',  // 使用实际的题库题量
                isFree: true, // 默认设为免费
                knowId: knowledgeId,
                questionCount: questionCount,
                includeShortAnswer: kpConfig.includeShortAnswer || false,
                shortAnswerCount: kpConfig.shortAnswerCount || 0
            });

            console.log(`✅ 已添加知识点到全局状态: ${knowledgeName} (ID: ${knowledgeId})`);
        }

        // 如果当前页面有对应的复选框，选中它
        const checkbox = $(`#kp-${knowledgeId}`);
        if (checkbox.length > 0) {
            checkbox.prop('checked', true);
            console.log(`✅ 已选中知识点复选框: ${knowledgeId}`);
        }

        // 如果是自由组卷界面，添加知识点到容器
        if ($('#customPaperModal').length > 0) {
            console.log('🔧 添加知识点到自由组卷界面...');

            // 确保知识点信息映射存在
            if (typeof window.knowledgeInfo === 'undefined') {
                window.knowledgeInfo = {};
            }

            // 将知识点信息添加到全局映射中
            window.knowledgeInfo[knowledgeId] = {
                name: knowledgeName,
                topicCount: kpConfig.topicCount || 0,  // 使用实际的题库题量
                originalId: knowledgeId,
                knowId: knowledgeId
            };

            console.log(`✅ 已添加知识点信息到全局映射: ${knowledgeName} (ID: ${knowledgeId})`);

            // 将知识点添加到自由组卷知识点列表
            if (typeof customSelectedKnowledgePoints !== 'undefined') {
                const kpItemData = {
                    id: knowledgeId,
                    name: knowledgeName
                };
                customSelectedKnowledgePoints.push(kpItemData);

                // 创建知识点模板
                const uniqueId = new Date().getTime() + '-' + knowledgeId;
                const template = $('#knowledgePointItemTemplate').html();

                if (!template) {
                    console.warn('⚠️ 未找到知识点模板 #knowledgePointItemTemplate，跳过自由组卷界面配置');
                    return;
                }

                console.log(`🔧 模板替换前:`, {
                    uniqueId: uniqueId,
                    knowledgeId: knowledgeId,
                    knowledgeName: knowledgeName,
                    templateLength: template.length
                });

                let html = template
                    .replace(/{id}/g, uniqueId)
                    .replace(/{knowledgeId}/g, knowledgeId || '')
                    .replace(/{name}/g, knowledgeName || '未命名知识点');

                console.log(`🔧 模板替换后:`, {
                    htmlLength: html.length,
                    containsName: html.includes(knowledgeName),
                    nameInHtml: html.match(/<h5[^>]*>(.*?)<\/h5>/)?.[1] || 'not found'
                });

                // 添加到容器
                $('#knowledgePointsContainer').append(html);

                // 延迟处理，确保DOM元素完全渲染
                setTimeout(() => {
                    // 设置基础题量 - 使用data-id属性查找元素
                    const $kpElement = $(`.knowledge-point-item[data-id="${uniqueId}"]`);

                    if ($kpElement.length === 0) {
                        console.warn(`⚠️ 未找到知识点元素: data-id="${uniqueId}"`);
                        return;
                    }

                console.log(`🔧 配置知识点元素: ${knowledgeName}, 基础题量: ${questionCount}, 简答题: ${kpConfig.includeShortAnswer}, 简答题数量: ${kpConfig.shortAnswerCount}`);

                // 设置基础题量
                const questionCountInput = $kpElement.find('.knowledge-question-count');
                questionCountInput.val(questionCount);
                console.log(`✅ 设置基础题量: ${questionCount}`);

                // 设置简答题开关和数量
                const includeShortAnswerSwitch = $kpElement.find('.include-short-answer');
                const shortAnswerConfig = $kpElement.find('.short-answer-config');
                const shortAnswerCountInput = $kpElement.find('.short-answer-count');

                if (kpConfig.includeShortAnswer) {
                    // 开启简答题
                    includeShortAnswerSwitch.prop('checked', true);

                    // 显示简答题配置区域
                    shortAnswerConfig.show();

                    // 设置简答题数量
                    const shortAnswerCount = kpConfig.shortAnswerCount || 1;
                    shortAnswerCountInput.val(shortAnswerCount);

                    console.log(`✅ 已设置简答题: 开关=true, 数量=${shortAnswerCount}`);

                    // 触发change事件，确保UI更新
                    includeShortAnswerSwitch.trigger('change');
                    shortAnswerCountInput.trigger('input');
                } else {
                    // 关闭简答题
                    includeShortAnswerSwitch.prop('checked', false);
                    shortAnswerConfig.hide();
                    shortAnswerCountInput.val(0);

                    console.log(`✅ 已关闭简答题开关`);

                    // 触发change事件，确保UI更新
                    includeShortAnswerSwitch.trigger('change');
                }

                // 触发基础题量的change事件，更新总计
                questionCountInput.trigger('input');

                    console.log(`✅ 已添加知识点到自由组卷界面: ${knowledgeName} (ID: ${knowledgeId})`);
                }, 50); // 50ms延迟，确保DOM渲染完成
            }
        }
    });

    // 4. 更新选择计数和显示
    setTimeout(() => {
        console.log('🔧 更新知识点选择显示...');

        // 更新选择计数
        if (typeof window.selectedKnowledgePoints !== 'undefined') {
            const selectedCount = window.selectedKnowledgePoints.size;
            $('.selectedCount').text(selectedCount);
            console.log(`✅ 已更新选择计数: ${selectedCount}`);
        }

        // 更新选择计数器和显示
        if (typeof updateSelectionCounter === 'function') {
            updateSelectionCounter();
            console.log('✅ 已调用 updateSelectionCounter');
        }

        // 更新已选择知识点显示
        if (typeof updateSelectedKnowledgePointsDisplay === 'function') {
            updateSelectedKnowledgePointsDisplay();
            console.log('✅ 已调用 updateSelectedKnowledgePointsDisplay');
        }

        // 如果是自由组卷界面，还需要更新每个知识点的总计
        if ($('#customPaperModal').length > 0) {
            $('.knowledge-point-item').each(function() {
                const total = parseInt($(this).find('.knowledge-question-count').val()) || 0;
                const includeShortAnswer = $(this).find('.include-short-answer').is(':checked');
                const shortAnswerCount = includeShortAnswer ? (parseInt($(this).find('.short-answer-count').val()) || 0) : 0;
                const grandTotal = total + shortAnswerCount;

                $(this).find('.knowledge-point-total').text(grandTotal);
            });

            // 重新绑定知识点事件
            if (typeof bindKnowledgePointEvents === 'function') {
                bindKnowledgePointEvents();
            }

            // 强制刷新自由组卷界面的知识点配置显示
            if (typeof refreshCustomPaperKnowledgeConfig === 'function') {
                refreshCustomPaperKnowledgeConfig();
            }
        }

        // 存储知识点配置到表单数据
        $('#generatePaperForm').data('knowledgeConfigs', knowledgePointConfigs);
        console.log('✅ 已存储知识点配置到表单数据');

        // 确保全局知识点信息映射包含所有知识点
        if (typeof window.knowledgeInfo === 'undefined') {
            window.knowledgeInfo = {};
        }

        knowledgePointConfigs.forEach(kpConfig => {
            const knowledgeId = kpConfig.knowledgeId || kpConfig.knowId;
            const knowledgeName = kpConfig.knowledgeName || `知识点${knowledgeId}`;

            if (!window.knowledgeInfo[knowledgeId]) {
                window.knowledgeInfo[knowledgeId] = {
                    name: knowledgeName,
                    topicCount: kpConfig.topicCount || 0,
                    originalId: knowledgeId,
                    knowId: knowledgeId
                };
                console.log(`✅ 补充知识点信息到全局映射: ${knowledgeName} (ID: ${knowledgeId})`);
            }
        });

        // 刷新知识点配置显示
        refreshKnowledgePointConfigsDisplay(knowledgePointConfigs);

        console.log('✅ 知识点配置应用完成');
    }, 100);
}

/**
 *  获取选中的知识点配置
 */
function getSelectedKnowledgePointConfigs() {
    try {
        // 尝试从全局变量获取
        if (typeof window.currentKnowledgeConfigs !== 'undefined' && window.currentKnowledgeConfigs) {
            console.log('✅ 从全局变量获取知识点配置:', window.currentKnowledgeConfigs);
            return window.currentKnowledgeConfigs;
        }

        // 尝试从表单数据获取
        const formData = $('#generatePaperForm').data('knowledgeConfigs');
        if (formData && formData.length > 0) {
            console.log('✅ 从表单数据获取知识点配置:', formData);
            return formData;
        }

        // 尝试从paper-generate.js的全局状态获取
        if (typeof window.selectedKnowledgePoints !== 'undefined' && window.selectedKnowledgePoints) {
            const configs = [];
            window.selectedKnowledgePoints.forEach((pointData, pointId) => {
                // 确保 pointData 存在且有效
                if (!pointData || typeof pointData !== 'object') {
                    console.warn('⚠️ 无效的知识点数据:', pointData, 'ID:', pointId);
                    return;
                }

                const config = {
                    knowledgeId: pointData.knowId || pointId,
                    knowledgeName: pointData.name || `知识点${pointId}`,
                    questionCount: 5, // 默认题目数量
                    includeShortAnswer: false
                };
                configs.push(config);
            });

            if (configs.length > 0) {
                console.log('✅ 从selectedKnowledgePoints获取知识点配置:', configs);
                return configs;
            }
        }

        // 尝试从页面元素获取
        if (typeof getSelectedKnowledgePoints === 'function') {
            const result = getSelectedKnowledgePoints();
            if (result && result.length > 0) {
                console.log('✅ 从getSelectedKnowledgePoints函数获取知识点配置:', result);
                return result;
            }
        }

        // 最后尝试从已选择的知识点DOM元素获取
        const selectedElements = $('.selected-knowledge-point');
        if (selectedElements.length > 0) {
            const configs = [];
            selectedElements.each(function() {
                const $element = $(this);
                const knowledgeId = $element.data('know-id') || $element.data('id');
                const knowledgeName = $element.data('name') || $element.find('.card-title').text().trim();

                if (knowledgeId && knowledgeName) {
                    configs.push({
                        knowledgeId: knowledgeId,
                        knowledgeName: knowledgeName,
                        questionCount: 5, // 默认题目数量
                        includeShortAnswer: false
                    });
                }
            });

            if (configs.length > 0) {
                console.log('✅ 从DOM元素获取知识点配置:', configs);
                return configs;
            }
        }

        console.warn('⚠️ 无法获取知识点配置 - 所有方法都失败了');
        return [];
    } catch (error) {
        console.error('获取知识点配置失败:', error);
        return [];
    }
}

/**
 *  记录配置使用
 */
function recordConfigUsage(configId) {
    // 防止重复记录同一个配置的使用
    if (window.recordedUsageConfigs && window.recordedUsageConfigs.includes(configId)) {
        console.log('⚠️ 配置使用已记录，跳过重复记录:', configId);
        return;
    }

    // 初始化记录数组
    if (!window.recordedUsageConfigs) {
        window.recordedUsageConfigs = [];
    }

    // 添加到已记录列表
    window.recordedUsageConfigs.push(configId);

    $.ajax({
        url: `/api/paper-configs/${configId}/use`,
        method: 'POST',
        success: function(response) {
            console.log('✅ 配置使用记录成功');
        },
        error: function(xhr) {
            console.warn('⚠️ 配置使用记录失败:', xhr);
            // 失败时从记录列表中移除，允许重试
            const index = window.recordedUsageConfigs.indexOf(configId);
            if (index > -1) {
                window.recordedUsageConfigs.splice(index, 1);
            }
        }
    });
}

/**
 *  格式化日期
 */
function formatDate(dateString) {
    if (!dateString) return '未知';

    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return '今天';
        } else if (diffDays === 2) {
            return '昨天';
        } else if (diffDays <= 7) {
            return `${diffDays - 1}天前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    } catch (error) {
        console.error('日期格式化失败:', error);
        return '未知';
    }
}

/**
 * 刷新自由组卷界面的知识点配置显示
 */
function refreshCustomPaperKnowledgeConfig() {
    console.log('🔧 刷新自由组卷界面的知识点配置显示...');

    // 检查是否在自由组卷界面
    if ($('#customPaperModal').length === 0 || !$('#customPaperModal').hasClass('show')) {
        console.log('⚠️ 不在自由组卷界面，跳过刷新');
        return;
    }

    // 获取当前的知识点配置
    const knowledgeConfigs = $('#generatePaperForm').data('knowledgeConfigs');
    if (!knowledgeConfigs || knowledgeConfigs.length === 0) {
        console.log('⚠️ 没有知识点配置需要刷新');
        return;
    }

    console.log('🔧 开始刷新知识点配置显示:', knowledgeConfigs);

    // 如果存在 renderKnowledgePointsConfig 函数，调用它
    if (typeof renderKnowledgePointsConfig === 'function') {
        console.log('✅ 调用 renderKnowledgePointsConfig 函数');
        renderKnowledgePointsConfig(knowledgeConfigs);
    } else if (typeof renderKnowledgePointConfigs === 'function') {
        console.log('✅ 调用 renderKnowledgePointConfigs 函数（备用）');
        renderKnowledgePointConfigs(knowledgeConfigs);
    } else {
        console.log('⚠️ renderKnowledgePointsConfig 函数不存在，尝试其他方法');

        // 尝试触发知识点配置更新事件
        $(document).trigger('knowledgeConfigsUpdated', [knowledgeConfigs]);
    }
}

/**
 * 更新自由组卷界面的总分计算
 */
function updateCustomPaperTotalScore() {
    try {
        let totalScore = 0;

        // 计算各题型总分
        const singleChoiceTotal = (parseInt($('#customSingleChoiceCount').val()) || 0) * (parseFloat($('#customSingleChoiceScore').val()) || 0);
        const multipleChoiceTotal = (parseInt($('#customMultipleChoiceCount').val()) || 0) * (parseFloat($('#customMultipleChoiceScore').val()) || 0);
        const judgmentTotal = (parseInt($('#customJudgmentCount').val()) || 0) * (parseFloat($('#customJudgmentScore').val()) || 0);
        const fillTotal = (parseInt($('#customFillCount').val()) || 0) * (parseFloat($('#customFillScore').val()) || 0);
        const shortAnswerTotal = (parseInt($('#customShortAnswerCount').val()) || 0) * (parseFloat($('#customShortAnswerScore').val()) || 0);
        const subjectiveTotal = (parseInt($('#customSubjectiveCount').val()) || 0) * (parseFloat($('#customSubjectiveScore').val()) || 0);

        totalScore = singleChoiceTotal + multipleChoiceTotal + judgmentTotal + fillTotal + shortAnswerTotal + subjectiveTotal;

        // 更新各题型的总分显示
        $('.type-total-score').eq(0).text(singleChoiceTotal);
        $('.type-total-score').eq(1).text(multipleChoiceTotal);
        $('.type-total-score').eq(2).text(judgmentTotal);
        $('.type-total-score').eq(3).text(fillTotal);
        $('.type-total-score').eq(4).text(shortAnswerTotal);
        $('.type-total-score').eq(5).text(subjectiveTotal);

        // 更新总分
        $('#customTotalScore').text(totalScore);

        console.log('✅ 自由组卷界面总分更新完成:', totalScore);
    } catch (error) {
        console.error('❌ 更新自由组卷总分失败:', error);
    }
}

/**
 *  HTML转义
 */
function escapeHtml(text) {
    if (!text) return '';

    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };

    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 *  保存当前配置
 */
function saveCurrentConfig() {
    const formData = {
        configName: $('#configName').val().trim(),
        description: $('#configDescription').val().trim(),
        titleTemplate: $('#titleTemplate').val().trim(),
        isDefault: $('#setAsDefault').is(':checked'),
        isPublic: $('#makePublic').is(':checked')
    };

    // 验证表单
    if (!formData.configName) {
        showError('请输入配置名称');
        return;
    }

    // 获取当前页面配置
    const currentPageConfig = getCurrentPageConfig();
    if (!currentPageConfig) {
        showError('无法获取当前页面配置');
        return;
    }

    // 合并配置数据
    const configData = Object.assign({}, currentPageConfig, formData);

    //  检查是否为编辑模式和配置名称是否改变
    const isEditMode = window.currentEditingConfigId;
    const originalName = currentConfigData ? currentConfigData.configName : '';
    const isNameChanged = isEditMode && formData.configName !== originalName;

    //  智能决定是更新还是创建新配置
    let url, method, actionText;
    if (isEditMode && !isNameChanged) {
        // 编辑模式且名称未改变：更新原配置
        url = `/api/paper-configs/${window.currentEditingConfigId}`;
        method = 'PUT';
        actionText = '更新';
    } else {
        // 新建模式或编辑模式但名称已改变：创建新配置
        url = '/api/paper-configs';
        method = 'POST';
        actionText = isEditMode ? '另存为新配置' : '保存';
    }

    showLoading(`正在${actionText}配置...`, `${actionText}配置`);

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(configData),
        success: function(response) {
            console.log('🔧 配置保存成功，开始清理模态框状态...');

            // 立即隐藏加载模态框
            hideLoading();

            if (response.success) {
                currentConfigData = response.data;

                // 隐藏保存配置模态框
                $('#saveConfigModal').modal('hide');

                // 强制清理所有模态框状态，增加延迟确保完全清理
                setTimeout(function() {
                    console.log('🔧 强制清理模态框状态...');

                    // 移除所有可能的backdrop
                    $('.modal-backdrop').each(function() {
                        $(this).remove();
                    });

                    // 恢复body状态
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': '',
                        'position': '',
                        'top': ''
                    });

                    // 确保所有模态框都被隐藏
                    $('.modal').removeClass('show').hide();

                    console.log('✅ 模态框状态清理完成');

                    // 延迟显示成功提示，确保模态框完全关闭
                    setTimeout(function() {
                        console.log('🔧 显示保存成功提示...');

                        // 显示保存成功提示
                        Swal.fire({
                            icon: 'success',
                            title: `${actionText}成功`,
                            text: `配置已成功${actionText}，您可以在配置管理页面查看和管理所有配置`,
                            timer: 3000,
                            showConfirmButton: true,
                            confirmButtonText: '确定',
                            showCancelButton: true,
                            cancelButtonText: '查看配置',
                            cancelButtonColor: '#6c757d'
                        }).then((result) => {
                            if (result.dismiss === Swal.DismissReason.cancel) {
                                // 用户点击了"查看配置"按钮
                                window.open('/paper-configs', '_blank');
                            }
                        });
                    }, 300); // 增加延迟确保模态框完全关闭
                }, 200); // 增加延迟确保模态框隐藏动画完成

                //  处理编辑状态
                if (isEditMode) {
                    if (isNameChanged) {
                        // 名称已改变，创建了新配置，清除编辑状态
                        window.currentEditingConfigId = null;
                        document.title = '试卷生成 - 麦子教育系统';

                        // 显示特殊的另存为成功提示
                        setTimeout(() => {
                            Swal.fire({
                                icon: 'success',
                                title: '另存为新配置成功',
                                html: `
                                    <div class="text-center">
                                        <p>已创建新配置 "<strong>${escapeHtml(formData.configName)}</strong>"</p>
                                        <p class="text-muted">原配置 "<em>${escapeHtml(originalName)}</em>" 已保留</p>
                                        <div class="alert alert-info mt-3">
                                            <i class="fas fa-info-circle me-2"></i>
                                            您现在可以继续编辑当前配置，或选择其他操作
                                        </div>
                                    </div>
                                `,
                                confirmButtonText: '继续编辑',
                                showCancelButton: true,
                                cancelButtonText: '查看所有配置'
                            }).then((result) => {
                                if (result.dismiss === Swal.DismissReason.cancel) {
                                    window.open('/paper-configs', '_blank');
                                }
                            });
                        }, 500);
                    } else {
                        // 名称未改变，更新了原配置，保持编辑状态
                        // 更新当前配置数据
                        currentConfigData = response.data;

                        // 显示更新成功提示
                        setTimeout(() => {
                            Swal.fire({
                                icon: 'success',
                                title: '配置更新成功',
                                html: `
                                    <div class="text-center">
                                        <p>配置 "<strong>${escapeHtml(formData.configName)}</strong>" 已更新</p>
                                        <div class="alert alert-success mt-3">
                                            <i class="fas fa-check-circle me-2"></i>
                                            您可以继续编辑此配置，或退出编辑模式
                                        </div>
                                    </div>
                                `,
                                confirmButtonText: '继续编辑',
                                showCancelButton: true,
                                cancelButtonText: '退出编辑'
                            }).then((result) => {
                                if (result.dismiss === Swal.DismissReason.cancel) {
                                    // 用户选择退出编辑模式
                                    window.currentEditingConfigId = null;
                                    document.title = '试卷生成 - 麦子教育系统';
                                }
                            });
                        }, 500);
                    }
                }

                updateSaveButtonState();
            } else {
                showError(`${actionText}配置失败: ` + response.message);
            }
        },
        error: function(xhr) {
            hideLoading();
            let errorMessage = `${actionText}配置失败，请稍后重试`;

            // 尝试解析错误响应
            try {
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                }
            } catch (e) {
                console.warn('无法解析错误响应:', e);
            }

            // 特殊处理配置名称重复的错误
            if (errorMessage.includes('配置名称已存在') || errorMessage.includes('uk_user_config_name')) {
                errorMessage = '配置名称已存在，请使用不同的名称';

                // 自动生成新的配置名称
                const newName = generateDefaultConfigName();
                $('#configName').val(newName);

                Swal.fire({
                    icon: 'warning',
                    title: '配置名称重复',
                    html: `
                        <p>配置名称已存在，已为您生成新的名称：</p>
                        <p><strong>${newName}</strong></p>
                        <p>您可以修改后重新保存</p>
                    `,
                    showCancelButton: true,
                    confirmButtonText: '使用新名称保存',
                    cancelButtonText: '手动修改'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 用户选择使用新名称，重新提交
                        $('#saveConfigSubmitBtn').click();
                    } else {
                        // 用户选择手动修改，重新打开模态框
                        $('#saveConfigModal').modal('show');
                        $('#configName').focus().select();
                    }
                });
                return;
            }

            showError(errorMessage);
            console.error(`${actionText}配置失败:`, xhr);
        }
    });
}

/**
 *  获取当前页面配置
 */
function getCurrentPageConfig() {
    try {
        // 获取选中的知识点
        const knowledgePointConfigs = getSelectedKnowledgePointConfigs();
        if (!knowledgePointConfigs || knowledgePointConfigs.length === 0) {
            throw new Error('没有选中的知识点');
        }

        //  从试卷生成模态框获取配置
        const config = {
            knowledgePointConfigs: knowledgePointConfigs,

            // 题型配置
            singleChoiceCount: parseInt($('#singleChoiceCount').val()) || 0,
            singleChoiceScore: parseFloat($('#singleChoiceScore').val()) || 0,
            multipleChoiceCount: parseInt($('#multipleChoiceCount').val()) || 0,
            multipleChoiceScore: parseFloat($('#multipleChoiceScore').val()) || 0,
            judgmentCount: parseInt($('#judgmentCount').val()) || 0,
            judgmentScore: parseFloat($('#judgmentScore').val()) || 0,
            fillCount: parseInt($('#fillCount').val()) || 0,
            fillScore: parseFloat($('#fillScore').val()) || 0,
            shortAnswerCount: parseInt($('#shortAnswerCount').val()) || 0,
            shortAnswerScore: parseFloat($('#shortAnswerScore').val()) || 0,

            // 试卷基本信息
            paperType: $('#paperType').val() || 'standard',
            paperCount: parseInt($('#paperCount').val()) || 1,

            // 难度分布
            difficultyDistribution: {
                easy: (parseFloat($('#easyPercentage').val()) || 30) / 100,
                medium: (parseFloat($('#mediumPercentage').val()) || 50) / 100,
                hard: (parseFloat($('#hardPercentage').val()) || 20) / 100
            }
        };

        console.log('🔧 获取到的页面配置:', config);
        return config;
    } catch (error) {
        console.error('获取当前页面配置失败:', error);
        return null;
    }
}

/**
 *  检查是否有选中的知识点
 */
function hasSelectedKnowledgePoints() {
    // 1. 检查全局状态中的选中知识点
    if (typeof window.selectedKnowledgePoints !== 'undefined' && window.selectedKnowledgePoints && window.selectedKnowledgePoints.size > 0) {
        console.log('✅ 从全局状态检测到选中的知识点:', window.selectedKnowledgePoints.size);
        return true;
    }

    // 2. 检查知识点选择计数器
    const selectedCount = $('.selectedCount').first().text();
    const hasSelected = selectedCount && parseInt(selectedCount) > 0;
    if (hasSelected) {
        console.log('✅ 从计数器检测到选中的知识点:', selectedCount);
        return true;
    }

    // 3. 检查已选择知识点的DOM元素
    const selectedElements = $('.selected-knowledge-point');
    if (selectedElements.length > 0) {
        console.log('✅ 从DOM元素检测到选中的知识点:', selectedElements.length);
        return true;
    }

    // 4. 检查是否有知识点配置数据
    const knowledgeConfigs = getSelectedKnowledgePointConfigs();
    const hasConfigs = knowledgeConfigs && knowledgeConfigs.length > 0;
    if (hasConfigs) {
        console.log('✅ 从配置数据检测到选中的知识点:', knowledgeConfigs.length);
        return true;
    }

    // 5. 检查选中的复选框
    const checkedBoxes = $('.knowledge-checkbox:checked');
    if (checkedBoxes.length > 0) {
        console.log('✅ 从复选框检测到选中的知识点:', checkedBoxes.length);
        return true;
    }

    console.log('⚠️ 未检测到任何选中的知识点');
    return false;
}

/**
 *  更新保存按钮状态
 */
function updateSaveButtonState() {
    const hasKnowledgePoints = hasSelectedKnowledgePoints();

    // 更新页面顶部的保存按钮
    $('#saveConfigBtn').prop('disabled', !hasKnowledgePoints);

    // 更新模态框内的保存按钮
    $('#saveConfigBtnModal').prop('disabled', !hasKnowledgePoints);

    // 更新按钮提示文本
    const tooltipText = hasKnowledgePoints ? '保存当前配置' : '请先选择知识点';
    $('#saveConfigBtn, #saveConfigBtnModal').attr('title', tooltipText);
}

/**
 *  生成默认配置名称
 */
function generateDefaultConfigName() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');

    // 添加随机数确保唯一性
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

    return `试卷配置_${year}${month}${day}_${hour}${minute}${second}_${random}`;
}

/**
 *  生成默认标题模板
 */
function generateDefaultTitleTemplate() {
    return '{{科目}}考试试卷';
}

/**
 *  工具函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showLoading(message = '加载中...', title = '正在处理') {
    $('#loadingTitle').text(title);
    $('#loadingMessage').text(message);
    $('#loadingModal').modal('show');
}

function hideLoading() {
    try {
        console.log('🔧 开始关闭加载模态框...');

        // 检查模态框是否存在且可见
        const $loadingModal = $('#loadingModal');
        if ($loadingModal.length === 0) {
            console.warn('⚠️ 加载模态框不存在');
            return;
        }

        // 如果模态框已经隐藏，直接返回
        if (!$loadingModal.hasClass('show') && !$loadingModal.is(':visible')) {
            console.log('✅ 加载模态框已经隐藏');
            return;
        }

        // 立即隐藏模态框，不等待动画
        $loadingModal.removeClass('show').hide();
        console.log('🔧 已强制隐藏加载模态框');

        // 立即清理backdrop和body状态
        setTimeout(function() {
            // 移除所有可能的backdrop（包括加载模态框的）
            $('.modal-backdrop').each(function() {
                const $backdrop = $(this);
                console.log('🔧 移除backdrop:', $backdrop);
                $backdrop.remove();
            });

            // 恢复body状态
            $('body').removeClass('modal-open').css({
                'overflow': '',
                'padding-right': '',
                'position': '',
                'top': ''
            });

            // 确保加载模态框完全隐藏
            $loadingModal.css('display', 'none');

            console.log('✅ 加载模态框关闭完成');
        }, 50); // 减少延迟，立即清理

    } catch (error) {
        console.error('❌ 关闭加载模态框失败:', error);

        // 强制清理
        try {
            $('#loadingModal').hide().removeClass('show').css('display', 'none');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open').css({
                'overflow': '',
                'padding-right': '',
                'position': '',
                'top': ''
            });
            console.log('✅ 强制清理完成');
        } catch (cleanupError) {
            console.error('❌ 强制清理也失败:', cleanupError);
        }
    }
}

function showSuccess(message) {
    Swal.fire({
        icon: 'success',
        title: '成功',
        text: message,
        timer: 2000,
        showConfirmButton: false
    });
}

function showError(message) {
    Swal.fire({
        icon: 'error',
        title: '错误',
        text: message
    });
}

/**
 *  处理配置文件导入
 */
function handleConfigFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.endsWith('.json')) {
        showError('请选择JSON格式的配置文件');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const configData = JSON.parse(e.target.result);

            // 验证配置数据格式
            if (!validateImportedConfig(configData)) {
                showError('配置文件格式不正确');
                return;
            }

            // 应用导入的配置
            applyImportedConfig(configData);
            $('#loadConfigModal').modal('hide');

            showSuccess('配置导入成功');
        } catch (error) {
            console.error('配置文件解析失败:', error);
            showError('配置文件格式错误，请检查文件内容');
        }
    };

    reader.readAsText(file);

    // 清空文件输入，允许重复选择同一文件
    event.target.value = '';
}

/**
 *  验证导入的配置数据
 */
function validateImportedConfig(config) {
    // 检查必要字段
    const requiredFields = ['configName', 'knowledgePointConfigs'];
    for (const field of requiredFields) {
        if (!config[field]) {
            console.error('缺少必要字段:', field);
            return false;
        }
    }

    // 检查知识点配置
    if (!Array.isArray(config.knowledgePointConfigs) || config.knowledgePointConfigs.length === 0) {
        console.error('知识点配置无效');
        return false;
    }

    return true;
}

/**
 *  应用导入的配置
 */
function applyImportedConfig(config) {
    try {
        console.log('🔧 应用导入的配置:', config);

        // 应用配置到页面
        applyConfigToPage(config);

        console.log('✅ 导入配置应用完成');
    } catch (error) {
        console.error('应用导入配置失败:', error);
        showError('应用配置失败: ' + error.message);
    }
}

/**
 *  导出单个配置
 */
function exportSingleConfig(configId) {
    // 阻止事件冒泡，避免触发卡片选择
    event.stopPropagation();

    // 从当前配置列表中找到配置
    const config = availableConfigs.find(c => c.id === configId);
    if (!config) {
        showError('配置不存在');
        return;
    }

    // 准备导出数据
    const exportData = {
        configName: config.configName + '_导出',
        description: config.description,
        titleTemplate: config.titleTemplate,
        paperType: config.paperType,
        paperCount: config.paperCount,

        // 题型配置
        singleChoiceCount: config.singleChoiceCount,
        singleChoiceScore: config.singleChoiceScore,
        multipleChoiceCount: config.multipleChoiceCount,
        multipleChoiceScore: config.multipleChoiceScore,
        judgmentCount: config.judgmentCount,
        judgmentScore: config.judgmentScore,
        fillCount: config.fillCount,
        fillScore: config.fillScore,
        shortAnswerCount: config.shortAnswerCount,
        shortAnswerScore: config.shortAnswerScore,

        // 其他配置
        difficultyDistribution: config.difficultyDistribution,
        knowledgePointConfigs: config.knowledgePointConfigs,

        // 导出信息
        exportTime: new Date().toISOString(),
        exportVersion: '1.0'
    };

    // 下载文件
    downloadConfigAsFile(exportData, config.configName);
}

/**
 *  下载配置为文件
 */
function downloadConfigAsFile(configData, fileName) {
    try {
        const jsonString = JSON.stringify(configData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `${fileName}_配置.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showSuccess('配置导出成功');
    } catch (error) {
        console.error('导出配置失败:', error);
        showError('导出配置失败: ' + error.message);
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
        return '刚刚';
    } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前';
    } else if (diff < 604800000) { // 1周内
        return Math.floor(diff / 86400000) + '天前';
    } else {
        return date.toLocaleDateString();
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 导出函数供其他脚本使用
window.PaperConfigIntegration = {
    loadConfigById: loadConfigById,
    applyConfigToPage: applyConfigToPage,
    getCurrentPageConfig: getCurrentPageConfig,
    updateSaveButtonState: updateSaveButtonState
};

//  页面加载完成后初始化
$(document).ready(function() {
    console.log('🔧 paper-config-integration.js 开始初始化...');

    // 初始化配置集成功能
    initializeConfigIntegration();

    // 检查URL参数中的配置ID
    checkForConfigParameter();

    // 初始化保存按钮状态
    updateSaveButtonState();

    console.log('✅ paper-config-integration.js 初始化完成');
});
