/**
 *  试卷配置管理 JavaScript
 * 功能：配置的增删改查、导入导出、搜索筛选等
 */

// 全局变量
let currentConfigs = [];
let selectedConfigs = [];
let currentFilter = 'all';
let currentSort = 'recent';
let includePublic = false;

// 页面加载完成后初始化
$(document).ready(function() {
    console.log('🔧 配置管理页面初始化...');

    try {
        initializeEventListeners();
        loadConfigStatistics();
        loadConfigs();
    } catch (error) {
        console.error('❌ 页面初始化失败:', error);
        // 确保加载遮罩被隐藏
        hideLoading();
    }
});

/**
 *  初始化事件监听器
 */
function initializeEventListeners() {
    console.log('🔧 初始化事件监听器...');

    try {
        // 搜索输入框
        $('#searchInput').on('input', debounce(handleSearch, 300));

        // 包含公共配置复选框
        $('#includePublicConfigs').on('change', function() {
            includePublic = $(this).is(':checked');
            loadConfigs();
        });

        // 刷新按钮
        $('#refreshBtn').on('click', function() {
            loadConfigs();
            loadConfigStatistics();
        });

        // 排序选项
        $('.dropdown-menu a[data-sort]').on('click', function(e) {
            e.preventDefault();
            currentSort = $(this).data('sort');
            loadConfigs();
        });

        // 筛选标签
        $('#filterTabs a[data-filter]').on('click', function(e) {
            e.preventDefault();
            $('#filterTabs .nav-link').removeClass('active');
            $(this).addClass('active');
            currentFilter = $(this).data('filter');
            loadConfigs();
        });

        // 新建配置按钮
        $('#createConfigBtn').on('click', function() {
            try {
                createNewConfig();
            } catch (error) {
                console.error('❌ 创建新配置失败:', error);
                showError('创建新配置失败，请稍后重试');
            }
        });

        // 导入配置按钮
        $('#importConfigBtn').on('click', function() {
            $('#fileInput').click();
        });

        // 文件选择
        $('#fileInput').on('change', function(event) {
            try {
                handleFileImport(event);
            } catch (error) {
                console.error('❌ 文件导入失败:', error);
                showError('文件导入失败，请稍后重试');
            }
        });

        // 导出选中配置
        $('#exportSelectedBtn').on('click', function() {
            try {
                exportSelectedConfigs();
            } catch (error) {
                console.error('❌ 导出配置失败:', error);
                showError('导出配置失败，请稍后重试');
            }
        });

        // 回到顶部
        $('#scrollTopBtn').on('click', function() {
            $('html, body').animate({scrollTop: 0}, 500);
        });

        // 滚动事件
        $(window).on('scroll', function() {
            if ($(window).scrollTop() > 300) {
                $('#scrollTopBtn').fadeIn();
            } else {
                $('#scrollTopBtn').fadeOut();
            }
        });

        console.log('✅ 事件监听器初始化完成');
    } catch (error) {
        console.error('❌ 事件监听器初始化失败:', error);
        // 确保加载遮罩被隐藏
        hideLoading();
        throw error;
    }
}

/**
 *  防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 *  加载配置统计信息
 */
function loadConfigStatistics() {
    console.log('🔧 开始加载配置统计信息...');

    $.ajax({
        url: '/api/paper-configs/statistics',
        method: 'GET',
        beforeSend: function(xhr) {
            // 添加认证头
            const token = localStorage.getItem('token');
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        },
        success: function(response) {
            console.log('✅ 统计信息加载成功:', response);
            if (response.success) {
                renderStatistics(response.data);
            } else {
                console.warn('⚠️ 统计信息响应失败:', response.message);
                renderDefaultStatistics();
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ 加载统计信息失败:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
                error: error
            });

            // 显示默认统计信息
            renderDefaultStatistics();

            // 如果是401错误，可能需要重新登录
            if (xhr.status === 401) {
                console.warn('⚠️ 认证失败，可能需要重新登录');
            }
        }
    });
}

/**
 *  渲染统计信息
 */
function renderStatistics(stats) {
    const statsHtml = `
        <div class="stat-card">
            <div class="stat-value">${stats.totalConfigs || 0}</div>
            <div class="stat-label">我的配置</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.publicConfigs || 0}</div>
            <div class="stat-label">公共配置</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.recentlyUsed || 0}</div>
            <div class="stat-label">最近使用</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.mostUsedConfig ? stats.mostUsedConfig.usageCount : 0}</div>
            <div class="stat-label">最高使用次数</div>
        </div>
    `;
    $('#statsCards').html(statsHtml);
}

/**
 *  渲染默认统计信息
 */
function renderDefaultStatistics() {
    const defaultStatsHtml = `
        <div class="stat-card">
            <div class="stat-value">0</div>
            <div class="stat-label">我的配置</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">0</div>
            <div class="stat-label">公共配置</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">0</div>
            <div class="stat-label">最近使用</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">0</div>
            <div class="stat-label">最高使用次数</div>
        </div>
    `;
    $('#statsCards').html(defaultStatsHtml);
}

/**
 *  加载配置列表
 */
function loadConfigs() {
    console.log('🔧 开始加载配置列表...');
    showLoading();

    let url = '/api/paper-configs';
    const params = new URLSearchParams();

    if (includePublic) {
        params.append('includePublic', 'true');
    }

    if (params.toString()) {
        url += '?' + params.toString();
    }

    console.log('🔧 请求URL:', url);

    $.ajax({
        url: url,
        method: 'GET',
        beforeSend: function(xhr) {
            // 添加认证头
            const token = localStorage.getItem('token');
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        },
        success: function(response) {
            console.log('✅ 配置列表加载成功:', response);
            hideLoading();
            if (response.success) {
                currentConfigs = response.data || [];
                filterAndRenderConfigs();
            } else {
                console.warn('⚠️ 配置列表响应失败:', response.message);
                showError('加载配置失败: ' + response.message);
                currentConfigs = [];
                renderConfigs([]);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ 加载配置列表失败:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
                error: error
            });
            hideLoading();

            if (xhr.status === 401) {
                showError('认证失败，请重新登录');
                // 可以考虑跳转到登录页面
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            } else if (xhr.status === 404) {
                showError('API接口不存在，请检查服务器配置');
            } else {
                showError('加载配置失败，请稍后重试');
            }

            currentConfigs = [];
            renderConfigs([]);
        }
    });
}

/**
 *  筛选和渲染配置
 */
function filterAndRenderConfigs() {
    let filteredConfigs = [...currentConfigs];

    // 应用筛选
    switch (currentFilter) {
        case 'my':
            // 这里需要根据当前用户ID筛选，暂时跳过
            break;
        case 'default':
            filteredConfigs = filteredConfigs.filter(config => config.isDefault);
            break;
        case 'public':
            filteredConfigs = filteredConfigs.filter(config => config.isPublic);
            break;
        case 'recent':
            filteredConfigs = filteredConfigs.filter(config => config.lastUsedAt);
            break;
    }

    // 应用搜索
    const searchTerm = $('#searchInput').val().toLowerCase();
    if (searchTerm) {
        filteredConfigs = filteredConfigs.filter(config =>
            config.configName.toLowerCase().includes(searchTerm) ||
            (config.description && config.description.toLowerCase().includes(searchTerm))
        );
    }

    // 应用排序
    switch (currentSort) {
        case 'recent':
            filteredConfigs.sort((a, b) => new Date(b.lastUsedAt || 0) - new Date(a.lastUsedAt || 0));
            break;
        case 'usage':
            filteredConfigs.sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0));
            break;
        case 'name':
            filteredConfigs.sort((a, b) => a.configName.localeCompare(b.configName));
            break;
        case 'created':
            filteredConfigs.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            break;
    }

    renderConfigs(filteredConfigs);
}

/**
 *  渲染配置列表
 */
function renderConfigs(configs) {
    const container = $('#configsList');

    if (configs.length === 0) {
        container.empty();
        $('#emptyState').show();
        return;
    }

    $('#emptyState').hide();

    const configsHtml = configs.map(config => createConfigCard(config)).join('');
    container.html(configsHtml);
}

/**
 *  创建配置卡片
 */
function createConfigCard(config) {
    const totalQuestions = config.totalQuestions || 0;
    const totalScore = config.totalScore || 0;
    const lastUsed = config.lastUsedAt ? formatDate(config.lastUsedAt) : '从未使用';

    let headerClass = 'config-header';
    let badges = '';

    if (config.isDefault) {
        headerClass += ' default';
        badges += '<span class="badge bg-warning config-badge">默认</span>';
    }

    if (config.isPublic) {
        headerClass += ' public';
        badges += '<span class="badge bg-info config-badge">公共</span>';
    }

    return `
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="config-card" data-config-id="${config.id}">
                <div class="${headerClass}">
                    ${badges}
                    <h5 class="mb-1">${escapeHtml(config.configName)}</h5>
                    <p class="mb-0 small">${escapeHtml(config.description || '暂无描述')}</p>
                    <div class="config-stats">
                        <span><i class="fas fa-eye me-1"></i>${config.usageCount || 0}次使用</span>
                        <span><i class="fas fa-clock me-1"></i>${lastUsed}</span>
                    </div>
                </div>
                <div class="config-body">
                    <div class="config-summary">
                        <div class="summary-item">
                            <div class="summary-value">${totalQuestions}</div>
                            <div class="summary-label">总题数</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-value">${totalScore}</div>
                            <div class="summary-label">总分值</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-value">${config.paperCount || 1}</div>
                            <div class="summary-label">生成套数</div>
                        </div>
                    </div>
                    <div class="config-actions">
                        <button class="btn btn-primary btn-action" onclick="useConfig(${config.id})">
                            <i class="fas fa-play me-1"></i>使用
                        </button>
                        <button class="btn btn-outline-secondary btn-action" onclick="editConfig(${config.id})">
                            <i class="fas fa-edit me-1"></i>编辑
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-action dropdown-toggle"
                                    type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="copyConfig(${config.id})">
                                    <i class="fas fa-copy me-2"></i>复制
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportConfig(${config.id})">
                                    <i class="fas fa-download me-2"></i>导出
                                </a></li>
                                ${!config.isDefault ? `
                                <li><a class="dropdown-item" href="#" onclick="setDefaultConfig(${config.id})">
                                    <i class="fas fa-star me-2"></i>设为默认
                                </a></li>
                                ` : ''}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteConfig(${config.id})">
                                    <i class="fas fa-trash me-2"></i>删除
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 *  搜索处理
 */
function handleSearch() {
    filterAndRenderConfigs();
}

/**
 *  使用配置
 */
function useConfig(configId) {
    // 记录使用
    $.ajax({
        url: `/api/paper-configs/${configId}/use`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                // 跳转到试卷生成页面，并应用配置
                window.location.href = `/papers/generate?configId=${configId}`;
            }
        },
        error: function(xhr) {
            console.error('记录配置使用失败:', xhr);
            // 即使记录失败也跳转
            window.location.href = `/papers/generate?configId=${configId}`;
        }
    });
}

/**
 *  编辑配置
 */
function editConfig(configId) {
    // 跳转到试卷生成页面并加载配置进行编辑
    window.location.href = `/papers/generate?configId=${configId}&mode=edit`;
}

/**
 *  复制配置
 */
function copyConfig(configId) {
    Swal.fire({
        title: '复制配置',
        text: '请输入新配置的名称:',
        input: 'text',
        inputPlaceholder: '新配置名称',
        showCancelButton: true,
        confirmButtonText: '复制',
        cancelButtonText: '取消',
        inputValidator: (value) => {
            if (!value) {
                return '请输入配置名称';
            }
            if (value.length > 100) {
                return '配置名称不能超过100个字符';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const newConfigName = result.value;

            $.ajax({
                url: `/api/paper-configs/${configId}/copy`,
                method: 'POST',
                data: { newConfigName: newConfigName },
                success: function(response) {
                    if (response.success) {
                        showSuccess('配置复制成功');
                        loadConfigs();
                    } else {
                        showError('复制失败: ' + response.message);
                    }
                },
                error: function(xhr) {
                    showError('复制失败，请稍后重试');
                    console.error('复制配置失败:', xhr);
                }
            });
        }
    });
}

/**
 *  导出配置
 */
function exportConfig(configId) {
    window.open(`/api/paper-configs/${configId}/export`, '_blank');
}

/**
 *  设置默认配置
 */
function setDefaultConfig(configId) {
    Swal.fire({
        title: '设置默认配置',
        text: '确定要将此配置设为默认配置吗？',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/api/paper-configs/${configId}/set-default`,
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        showSuccess('默认配置设置成功');
                        loadConfigs();
                    } else {
                        showError('设置失败: ' + response.message);
                    }
                },
                error: function(xhr) {
                    showError('设置失败，请稍后重试');
                    console.error('设置默认配置失败:', xhr);
                }
            });
        }
    });
}

/**
 *  删除配置
 */
function deleteConfig(configId) {
    Swal.fire({
        title: '删除配置',
        text: '确定要删除此配置吗？此操作不可撤销！',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/api/paper-configs/${configId}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        showSuccess('配置删除成功');
                        loadConfigs();
                    } else {
                        showError('删除失败: ' + response.message);
                    }
                },
                error: function(xhr) {
                    showError('删除失败，请稍后重试');
                    console.error('删除配置失败:', xhr);
                }
            });
        }
    });
}

/**
 *  创建新配置
 */
function createNewConfig() {
    window.location.href = '/paper-configs/create';
}

/**
 *  处理文件导入
 */
function handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.json')) {
        showError('请选择JSON格式的配置文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    showLoading();

    $.ajax({
        url: '/api/paper-configs/import',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            hideLoading();
            if (response.success) {
                showSuccess(`成功导入 ${response.data.length} 个配置`);
                loadConfigs();
                loadConfigStatistics();
            } else {
                showError('导入失败: ' + response.message);
            }
        },
        error: function(xhr) {
            hideLoading();
            showError('导入失败，请检查文件格式');
            console.error('导入配置失败:', xhr);
        }
    });

    // 清空文件输入
    event.target.value = '';
}

/**
 *  工具函数
 */
function showLoading() {
    $('#loadingOverlay').show();
}

function hideLoading() {
    $('#loadingOverlay').hide();
}

function showSuccess(message) {
    Swal.fire({
        icon: 'success',
        title: '成功',
        text: message,
        timer: 2000,
        showConfirmButton: false
    });
}

function showError(message) {
    Swal.fire({
        icon: 'error',
        title: '错误',
        text: message
    });
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
        return '刚刚';
    } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前';
    } else if (diff < 604800000) { // 1周内
        return Math.floor(diff / 86400000) + '天前';
    } else {
        return date.toLocaleDateString();
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 *  导出选中的配置
 */
function exportSelectedConfigs() {
    const selectedConfigs = [];
    $('.config-card input[type="checkbox"]:checked').each(function() {
        const configId = $(this).closest('.config-card').data('config-id');
        selectedConfigs.push(configId);
    });

    if (selectedConfigs.length === 0) {
        showError('请先选择要导出的配置');
        return;
    }

    // 创建导出请求
    const exportData = {
        configIds: selectedConfigs
    };

    $.ajax({
        url: '/api/paper-configs/export-batch',
        method: 'POST',
        data: JSON.stringify(exportData),
        contentType: 'application/json',
        beforeSend: function(xhr) {
            const token = localStorage.getItem('token');
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        },
        success: function(response) {
            if (response.success) {
                // 创建下载链接
                const blob = new Blob([JSON.stringify(response.data, null, 2)], {
                    type: 'application/json'
                });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `paper-configs-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                showSuccess(`成功导出 ${selectedConfigs.length} 个配置`);
            } else {
                showError('导出失败: ' + response.message);
            }
        },
        error: function(xhr) {
            console.error('导出配置失败:', xhr);
            showError('导出失败，请稍后重试');
        }
    });
}

/**
 *  切换公共配置显示
 */
function togglePublicConfigs() {
    includePublic = !includePublic;
    const btn = $('#togglePublicBtn');

    if (includePublic) {
        btn.removeClass('btn-outline-secondary').addClass('btn-secondary');
        btn.html('<i class="fas fa-eye me-1"></i>隐藏公共配置');
    } else {
        btn.removeClass('btn-secondary').addClass('btn-outline-secondary');
        btn.html('<i class="fas fa-eye-slash me-1"></i>显示公共配置');
    }

    loadConfigs();
}

/**
 *  批量删除选中的配置
 */
function deleteSelectedConfigs() {
    const selectedConfigs = [];
    $('.config-card input[type="checkbox"]:checked').each(function() {
        const configId = $(this).closest('.config-card').data('config-id');
        selectedConfigs.push(configId);
    });

    if (selectedConfigs.length === 0) {
        showError('请先选择要删除的配置');
        return;
    }

    Swal.fire({
        title: '批量删除配置',
        text: `确定要删除选中的 ${selectedConfigs.length} 个配置吗？此操作不可撤销！`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            const deletePromises = selectedConfigs.map(configId => {
                return $.ajax({
                    url: `/api/paper-configs/${configId}`,
                    method: 'DELETE',
                    beforeSend: function(xhr) {
                        const token = localStorage.getItem('token');
                        if (token) {
                            xhr.setRequestHeader('Authorization', 'Bearer ' + token);
                        }
                    }
                });
            });

            Promise.allSettled(deletePromises).then(results => {
                const successCount = results.filter(r => r.status === 'fulfilled').length;
                const failCount = results.length - successCount;

                if (failCount === 0) {
                    showSuccess(`成功删除 ${successCount} 个配置`);
                } else {
                    showError(`删除完成：成功 ${successCount} 个，失败 ${failCount} 个`);
                }

                loadConfigs();
                loadConfigStatistics();
            });
        }
    });
}
