/**
 *  下载功能测试脚本
 * 用于测试和调试下载功能
 */

// 测试下载功能
function testDownload() {
    console.log('🔧 开始测试下载功能...');

    // 测试简单下载
    const testUrl = '/debug/download-test';

    try {
        // 方法1：创建下载链接
        const downloadLink = document.createElement('a');
        downloadLink.href = testUrl;
        downloadLink.download = 'test-download.txt';
        downloadLink.style.display = 'none';
        downloadLink.target = '_blank';

        document.body.appendChild(downloadLink);
        downloadLink.click();

        setTimeout(() => {
            document.body.removeChild(downloadLink);
        }, 100);

        console.log('✅ 测试下载请求已发送');

    } catch (error) {
        console.error('❌ 测试下载失败:', error);
    }
}

// 测试试卷下载功能
function testPaperDownload(paperId = 123, format = 'pdf', version = 'standard') {
    console.log('🔧 开始测试试卷下载功能...', { paperId, format, version });

    const url = `/api/papers/download/${paperId}?format=${format}&paperType=${version}`;

    try {
        // 使用fetch检查响应
        fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf'
            }
        })
        .then(response => {
            console.log('📊 下载响应状态:', response.status, response.statusText);
            console.log('📊 响应头:', Object.fromEntries(response.headers.entries()));

            if (response.ok) {
                // 如果响应成功，创建下载链接
                const downloadLink = document.createElement('a');
                downloadLink.href = url;
                downloadLink.download = `试卷_${paperId}.${format}`;
                downloadLink.style.display = 'none';
                downloadLink.target = '_blank';

                document.body.appendChild(downloadLink);
                downloadLink.click();

                setTimeout(() => {
                    document.body.removeChild(downloadLink);
                }, 100);

                console.log('✅ 试卷下载请求已发送');
            } else {
                console.error('❌ 下载响应失败:', response.status, response.statusText);
            }
        })
        .catch(error => {
            console.error('❌ 下载请求失败:', error);

            // 备用方法：直接使用链接
            console.log('🔄 尝试备用下载方法...');
            window.open(url, '_blank');
        });

    } catch (error) {
        console.error('❌ 测试试卷下载失败:', error);
    }
}

//  测试防重复下载机制
function testDuplicateDownloadPrevention(paperId = 123, format = 'pdf', version = 'standard') {
    console.log('🔧 开始测试防重复下载机制...');

    const url = `/api/papers/download/${paperId}?format=${format}&paperType=${version}`;

    // 模拟下载代理工具的行为：快速发起多个请求
    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            console.log(`🔄 发起第 ${i + 1} 次下载请求...`);

            fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/pdf',
                    'User-Agent': 'Thunder' // 模拟迅雷
                }
            })
            .then(response => {
                console.log(`📊 第 ${i + 1} 次请求响应:`, response.status, response.statusText);
            })
            .catch(error => {
                console.log(`❌ 第 ${i + 1} 次请求失败:`, error.message);
            });
        }, i * 100); // 间隔100ms发起请求
    }

    // 检查服务器日志，应该只记录一次下载统计
    setTimeout(() => {
        console.log('✅ 防重复测试完成，请检查服务器日志确认只记录了一次下载统计');
    }, 2000);
}

// 调试下拉菜单
function debugDropdownMenus() {
    console.log('🔍 调试下拉菜单...');

    $('.enhanced-download-menu').each(function(index) {
        const $menu = $(this);
        const $dropdown = $menu.closest('.dropdown, .btn-group');
        const $toggle = $dropdown.find('.dropdown-toggle');

        console.log(`菜单 ${index + 1}:`, {
            menu: $menu[0],
            dropdown: $dropdown[0],
            toggle: $toggle[0],
            menuVisible: $menu.is(':visible'),
            menuHasShow: $menu.hasClass('show'),
            toggleOffset: $toggle.offset(),
            menuOffset: $menu.offset(),
            menuWidth: $menu.outerWidth(),
            menuHeight: $menu.outerHeight()
        });
    });
}

// 修复所有下拉菜单
function fixAllDropdowns() {
    console.log('🔧 修复所有下拉菜单...');

    if (typeof window.DropdownMenuFix !== 'undefined') {
        window.DropdownMenuFix.fixAllDropdownMenus();
        console.log('✅ 使用DropdownMenuFix修复完成');
    } else {
        console.warn('⚠️ DropdownMenuFix不可用');
    }
}

// 测试版本选择器
function testVersionSelector() {
    console.log('🔧 测试版本选择器...');

    $('.version-selector').each(function(index) {
        const $selector = $(this);
        const currentValue = $selector.val();

        console.log(`版本选择器 ${index + 1}:`, {
            element: $selector[0],
            currentValue: currentValue,
            options: $selector.find('option').map(function() {
                return { value: this.value, text: this.text };
            }).get()
        });
    });
}

// 导出测试函数
window.DownloadTest = {
    testDownload,
    testPaperDownload,
    testDuplicateDownloadPrevention,
    debugDropdownMenus,
    fixAllDropdowns,
    testVersionSelector
};

// 页面加载完成后自动运行一些测试
$(document).ready(function() {
    console.log('🔧 下载测试脚本已加载');

    // 添加测试按钮到页面（如果是调试模式）
    if (window.location.search.includes('debug=true')) {
        addDebugButtons();
    }
});

// 添加调试按钮
function addDebugButtons() {
    const debugPanel = $(`
        <div id="debug-panel" style="position: fixed; top: 10px; right: 10px; z-index: 9999; background: white; border: 1px solid #ccc; padding: 10px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 300px;">
            <h6>🔧 下载调试面板</h6>
            <div class="mb-2">
                <button class="btn btn-sm btn-primary" onclick="DownloadTest.testDownload()">测试下载</button>
                <button class="btn btn-sm btn-success" onclick="DownloadTest.testPaperDownload()">测试试卷下载</button>
            </div>
            <div class="mb-2">
                <button class="btn btn-sm btn-warning" onclick="DownloadTest.testDuplicateDownloadPrevention()">测试防重复</button>
                <button class="btn btn-sm btn-info" onclick="DownloadTest.debugDropdownMenus()">调试下拉菜单</button>
            </div>
            <div class="mb-2">
                <button class="btn btn-sm btn-secondary" onclick="DownloadTest.fixAllDropdowns()">修复下拉菜单</button>
                <button class="btn btn-sm btn-light" onclick="DownloadTest.testVersionSelector()">测试版本选择器</button>
            </div>
            <button class="btn btn-sm btn-danger w-100" onclick="$('#debug-panel').remove()">关闭调试面板</button>
        </div>
    `);

    $('body').append(debugPanel);
}
