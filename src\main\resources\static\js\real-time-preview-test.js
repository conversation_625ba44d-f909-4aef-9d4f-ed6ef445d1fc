/**
 * 实时预览功能测试脚本
 * 用于测试组卷界面的实时预览和KaTeX数学公式渲染
 */

// 测试实时预览功能
function testRealTimePreview() {
    console.log('=== 开始测试实时预览功能 ===');

    // 1. 检查预览容器是否存在
    const previewContainer = document.getElementById('realTimePreviewContainer');
    if (!previewContainer) {
        console.error('❌ 实时预览容器未找到');
        return false;
    }
    console.log('✅ 实时预览容器已找到');

    // 2. 检查PaperRealTimePreview类是否可用
    if (typeof PaperRealTimePreview === 'undefined') {
        console.error('❌ PaperRealTimePreview类未找到');
        return false;
    }
    console.log('✅ PaperRealTimePreview类已加载');

    // 3. 检查集成类是否可用
    if (typeof CustomPaperPreviewIntegration === 'undefined') {
        console.error('❌ CustomPaperPreviewIntegration类未找到');
        return false;
    }
    console.log('✅ CustomPaperPreviewIntegration类已加载');

    // 4. 检查KaTeX是否可用
    if (typeof katex === 'undefined') {
        console.warn('⚠️ KaTeX未加载，数学公式渲染可能不可用');
    } else {
        console.log('✅ KaTeX已加载');
    }

    if (typeof renderMathInElement === 'undefined') {
        console.warn('⚠️ KaTeX auto-render未加载，自动数学公式渲染可能不可用');
    } else {
        console.log('✅ KaTeX auto-render已加载');
    }

    return true;
}

// 测试数学公式渲染
function testMathRendering() {
    console.log('\n=== 开始测试数学公式渲染 ===');

    // 创建测试容器
    const testContainer = document.createElement('div');
    testContainer.id = 'mathTestContainer';
    testContainer.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h6>数学公式渲染测试</h6>
            </div>
            <div class="card-body">
                <p>行内公式测试: $x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$</p>
                <p>块级公式测试:</p>
                <p>$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$</p>
                <p>复杂公式测试:</p>
                <p>$$\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}$$</p>
            </div>
        </div>
    `;

    // 添加到预览容器
    const previewContainer = document.getElementById('realTimePreviewContainer');
    if (previewContainer) {
        previewContainer.appendChild(testContainer);

        // 渲染数学公式
        if (typeof renderMathInElement !== 'undefined') {
            try {
                renderMathInElement(testContainer, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\(', right: '\\)', display: false},
                        {left: '\\[', right: '\\]', display: true}
                    ],
                    throwOnError: false,
                    errorColor: '#cc0000',
                    strict: false
                });
                console.log('✅ 数学公式渲染成功');

                // 5秒后移除测试容器
                setTimeout(() => {
                    if (testContainer.parentNode) {
                        testContainer.parentNode.removeChild(testContainer);
                        console.log('🧹 测试容器已清理');
                    }
                }, 5000);

                return true;
            } catch (e) {
                console.error('❌ 数学公式渲染失败:', e);
                return false;
            }
        } else {
            console.error('❌ KaTeX auto-render不可用');
            return false;
        }
    } else {
        console.error('❌ 预览容器不可用');
        return false;
    }
}

// 测试实时预览更新
function testPreviewUpdate() {
    console.log('\n=== 开始测试实时预览更新 ===');

    // 检查是否有全局预览实例
    if (typeof window.realTimePreview !== 'undefined' && window.realTimePreview) {
        console.log('✅ 找到全局预览实例');

        // 触发预览更新
        try {
            window.realTimePreview.updatePreview();
            console.log('✅ 预览更新触发成功');
            return true;
        } catch (e) {
            console.error('❌ 预览更新失败:', e);
            return false;
        }
    } else {
        console.warn('⚠️ 全局预览实例未找到，尝试从集成实例获取');

        // 检查集成实例
        if (typeof window.customPaperPreviewIntegration !== 'undefined' && window.customPaperPreviewIntegration) {
            console.log('✅ 找到集成实例，尝试获取预览实例');

            const previewInstance = window.customPaperPreviewIntegration.getPreviewInstance();
            if (previewInstance) {
                console.log('✅ 从集成实例获取到预览实例');
                window.realTimePreview = previewInstance;

                try {
                    window.realTimePreview.updatePreview();
                    console.log('✅ 预览更新触发成功');
                    return true;
                } catch (e) {
                    console.error('❌ 预览更新失败:', e);
                    return false;
                }
            } else {
                console.warn('⚠️ 集成实例中没有预览实例，尝试初始化');

                try {
                    window.customPaperPreviewIntegration.initializePreview();
                    const newPreviewInstance = window.customPaperPreviewIntegration.getPreviewInstance();

                    if (newPreviewInstance) {
                        window.realTimePreview = newPreviewInstance;
                        console.log('✅ 预览实例初始化成功');

                        window.realTimePreview.updatePreview();
                        console.log('✅ 预览更新触发成功');
                        return true;
                    } else {
                        console.error('❌ 预览实例初始化失败');
                        return false;
                    }
                } catch (e) {
                    console.error('❌ 预览实例初始化异常:', e);
                    return false;
                }
            }
        } else {
            console.error('❌ 集成实例也未找到，尝试手动创建');

            try {
                if (typeof CustomPaperPreviewIntegration !== 'undefined') {
                    const integration = new CustomPaperPreviewIntegration();
                    integration.init();

                    setTimeout(() => {
                        const previewInstance = integration.getPreviewInstance();
                        if (previewInstance) {
                            window.realTimePreview = previewInstance;
                            window.customPaperPreviewIntegration = integration;
                            console.log('✅ 手动创建预览集成成功');
                        } else {
                            console.error('❌ 手动创建的集成实例中没有预览实例');
                        }
                    }, 1000);

                    return true;
                } else {
                    console.error('❌ CustomPaperPreviewIntegration 类未找到');
                    return false;
                }
            } catch (e) {
                console.error('❌ 手动创建预览集成失败:', e);
                return false;
            }
        }
    }
}

// 测试配置获取
function testConfigRetrieval() {
    console.log('\n=== 开始测试配置获取 ===');

    // 测试题型配置获取
    const typeCountMap = {
        'singleChoiceCount': parseInt($('#singleChoiceCount').val()) || 0,
        'multipleChoiceCount': parseInt($('#multipleChoiceCount').val()) || 0,
        'judgmentCount': parseInt($('#judgmentCount').val()) || 0,
        'fillCount': parseInt($('#fillCount').val()) || 0,
        'shortAnswerCount': parseInt($('#shortAnswerCount').val()) || 0
    };

    console.log('题型数量配置:', typeCountMap);

    // 测试题型分值获取（确保为整数）
    const typeScoreMap = {
        'singleChoiceScore': parseInt($('#singleChoiceScore').val()) || 0,
        'multipleChoiceScore': parseInt($('#multipleChoiceScore').val()) || 0,
        'judgmentScore': parseInt($('#judgmentScore').val()) || 0,
        'fillScore': parseInt($('#fillScore').val()) || 0,
        'shortAnswerScore': parseInt($('#shortAnswerScore').val()) || 0
    };

    console.log('题型分值配置:', typeScoreMap);

    // 测试试卷标题获取
    const paperTitle = $('#paperTitle').val() || '';
    console.log('试卷标题:', paperTitle);

    // 测试知识点配置获取
    const knowledgeConfigs = $('#generatePaperForm').data('knowledgeConfigs') || [];
    console.log('知识点配置:', knowledgeConfigs);

    return true;
}

// 测试预览请求数据格式
function testPreviewRequestFormat() {
    console.log('\n=== 开始测试预览请求数据格式 ===');

    if (!window.realTimePreview) {
        console.error('❌ 预览实例不存在');
        return false;
    }

    try {
        // 获取预览请求数据
        const requestData = window.realTimePreview.buildPreviewRequest();

        if (!requestData) {
            console.warn('⚠️ 预览请求数据为空');
            return false;
        }

        console.log('📋 预览请求数据:');
        console.log('- 标题:', requestData.title);
        console.log('- 题型数量配置:', requestData.typeCountMap);
        console.log('- 题型分值配置:', requestData.typeScoreMap);
        console.log('- 知识点配置:', requestData.knowledgePointConfigs);
        console.log('- 预览限制:', requestData.previewLimit);

        // 验证数据格式
        let isValid = true;

        // 验证知识点配置
        if (!requestData.knowledgePointConfigs || requestData.knowledgePointConfigs.length === 0) {
            console.error('❌ 知识点配置为空');
            isValid = false;
        } else {
            requestData.knowledgePointConfigs.forEach((config, index) => {
                if (typeof config.knowledgeId !== 'number' || config.knowledgeId <= 0) {
                    console.error(`❌ 知识点配置 ${index}: knowledgeId 必须为正整数，当前值:`, config.knowledgeId);
                    isValid = false;
                }
                if (typeof config.questionCount !== 'number' || config.questionCount < 0) {
                    console.error(`❌ 知识点配置 ${index}: questionCount 必须为非负整数，当前值:`, config.questionCount);
                    isValid = false;
                }
                if (typeof config.includeShortAnswer !== 'boolean') {
                    console.error(`❌ 知识点配置 ${index}: includeShortAnswer 必须为布尔值，当前值:`, config.includeShortAnswer);
                    isValid = false;
                }
            });
        }

        // 验证题型数量配置
        if (!requestData.typeCountMap || Object.keys(requestData.typeCountMap).length === 0) {
            console.error('❌ 题型数量配置为空');
            isValid = false;
        } else {
            Object.entries(requestData.typeCountMap).forEach(([type, count]) => {
                if (typeof count !== 'number' || count <= 0) {
                    console.error(`❌ 题型数量配置 ${type}: 数量必须为正整数，当前值:`, count);
                    isValid = false;
                }
            });
        }

        // 验证题型分值配置
        if (!requestData.typeScoreMap || Object.keys(requestData.typeScoreMap).length === 0) {
            console.error('❌ 题型分值配置为空');
            isValid = false;
        } else {
            Object.entries(requestData.typeScoreMap).forEach(([type, score]) => {
                if (typeof score !== 'number' || score <= 0) {
                    console.error(`❌ 题型分值配置 ${type}: 分值必须为正整数，当前值:`, score);
                    isValid = false;
                }
            });
        }

        if (isValid) {
            console.log('✅ 预览请求数据格式验证通过');

            // 显示完整的请求数据
            console.log('📤 完整请求数据:');
            console.log(JSON.stringify(requestData, null, 2));
        } else {
            console.log('❌ 预览请求数据格式验证失败');
        }

        return isValid;

    } catch (e) {
        console.error('❌ 测试预览请求数据格式时发生错误:', e);
        return false;
    }
}

// 模拟配置变化
function simulateConfigChange() {
    console.log('\n=== 开始模拟配置变化 ===');

    // 模拟修改题型数量
    $('#singleChoiceCount').val(10).trigger('change');
    $('#multipleChoiceCount').val(5).trigger('change');
    $('#judgmentCount').val(8).trigger('change');

    console.log('✅ 已模拟题型数量变化');

    // 模拟修改分值
    $('#singleChoiceScore').val(2).trigger('change');
    $('#multipleChoiceScore').val(4).trigger('change');
    $('#judgmentScore').val(1).trigger('change');

    console.log('✅ 已模拟题型分值变化');

    // 模拟修改试卷标题
    $('#paperTitle').val('数学测试卷 - 实时预览测试').trigger('change');

    console.log('✅ 已模拟试卷标题变化');

    return true;
}

// 主测试函数
function runRealTimePreviewTests() {
    console.log('🧪 开始运行实时预览测试套件');

    const results = {
        basicCheck: false,
        mathRendering: false,
        previewUpdate: false,
        configRetrieval: false,
        configChange: false
    };

    // 1. 基础检查
    results.basicCheck = testRealTimePreview();

    // 2. 数学公式渲染测试
    if (results.basicCheck) {
        results.mathRendering = testMathRendering();
    }

    // 3. 配置获取测试
    results.configRetrieval = testConfigRetrieval();

    // 4. 配置变化模拟
    results.configChange = simulateConfigChange();

    // 5. 预览更新测试
    setTimeout(() => {
        results.previewUpdate = testPreviewUpdate();

        // 输出测试结果
        console.log('\n📊 测试结果汇总:');
        console.log(`基础检查: ${results.basicCheck ? '✅' : '❌'}`);
        console.log(`数学公式渲染: ${results.mathRendering ? '✅' : '❌'}`);
        console.log(`配置获取: ${results.configRetrieval ? '✅' : '❌'}`);
        console.log(`配置变化模拟: ${results.configChange ? '✅' : '❌'}`);
        console.log(`预览更新: ${results.previewUpdate ? '✅' : '❌'}`);

        const passedTests = Object.values(results).filter(r => r).length;
        const totalTests = Object.keys(results).length;

        console.log(`\n🎯 测试完成: ${passedTests}/${totalTests} 项测试通过`);

        if (passedTests === totalTests) {
            console.log('🎉 所有测试通过！实时预览功能正常');
        } else {
            console.log('⚠️ 部分测试失败，请检查相关功能');
        }
    }, 1000);
}

// 强制初始化预览实例
function forceInitializePreview() {
    console.log('🔧 强制初始化预览实例...');

    // 检查预览容器是否存在
    const previewContainer = document.getElementById('realTimePreviewContainer');
    if (!previewContainer) {
        console.error('❌ 实时预览容器未找到，无法初始化');
        return false;
    }

    try {
        // 如果已有集成实例，先清理
        if (window.customPaperPreviewIntegration) {
            console.log('清理现有集成实例...');
        }

        // 创建新的集成实例
        const integration = new CustomPaperPreviewIntegration();

        // 手动初始化
        integration.init();

        // 等待初始化完成
        setTimeout(() => {
            integration.initializePreview();

            setTimeout(() => {
                const previewInstance = integration.getPreviewInstance();
                if (previewInstance) {
                    window.realTimePreview = previewInstance;
                    window.customPaperPreviewIntegration = integration;

                    console.log('✅ 预览实例强制初始化成功');
                    console.log('预览实例:', window.realTimePreview);

                    // 立即触发一次预览更新
                    try {
                        window.realTimePreview.updatePreview();
                        console.log('✅ 初始预览更新成功');
                    } catch (e) {
                        console.warn('⚠️ 初始预览更新失败:', e);
                    }

                    return true;
                } else {
                    console.error('❌ 强制初始化后仍无法获取预览实例');
                    return false;
                }
            }, 500);
        }, 200);

    } catch (e) {
        console.error('❌ 强制初始化异常:', e);
        return false;
    }
}

// 检查和修复预览实例
function checkAndFixPreview() {
    console.log('🔍 检查预览实例状态...');

    console.log('全局变量检查:');
    console.log('- window.realTimePreview:', typeof window.realTimePreview, window.realTimePreview);
    console.log('- window.customPaperPreviewIntegration:', typeof window.customPaperPreviewIntegration, window.customPaperPreviewIntegration);
    console.log('- window.PaperRealTimePreview:', typeof window.PaperRealTimePreview);
    console.log('- window.CustomPaperPreviewIntegration:', typeof window.CustomPaperPreviewIntegration);

    // 检查预览容器
    const previewContainer = document.getElementById('realTimePreviewContainer');
    console.log('- 预览容器:', previewContainer ? '✅ 存在' : '❌ 不存在');

    // 检查模态框状态
    const modal = document.getElementById('paperGenerationModal');
    const isModalVisible = modal && $(modal).hasClass('show');
    console.log('- 模态框状态:', isModalVisible ? '✅ 已显示' : '❌ 未显示');

    if (!window.realTimePreview) {
        console.log('⚠️ 预览实例缺失，尝试修复...');

        if (!isModalVisible) {
            console.log('💡 建议：请先打开试卷生成模态框，然后重新运行测试');
            return false;
        }

        return forceInitializePreview();
    } else {
        console.log('✅ 预览实例存在');
        return true;
    }
}

// 导出测试函数
window.testRealTimePreview = testRealTimePreview;
window.testMathRendering = testMathRendering;
window.testPreviewUpdate = testPreviewUpdate;
window.testConfigRetrieval = testConfigRetrieval;
window.testPreviewRequestFormat = testPreviewRequestFormat;
window.simulateConfigChange = simulateConfigChange;
window.runRealTimePreviewTests = runRealTimePreviewTests;
window.forceInitializePreview = forceInitializePreview;
window.checkAndFixPreview = checkAndFixPreview;

console.log('🧪 实时预览测试脚本已加载');
console.log('可用的测试函数:');
console.log('- runRealTimePreviewTests(): 运行完整测试套件');
console.log('- checkAndFixPreview(): 检查和修复预览实例');
console.log('- forceInitializePreview(): 强制初始化预览实例');
console.log('- testPreviewRequestFormat(): 测试预览请求数据格式');
console.log('- testMathRendering(): 测试数学公式渲染');
console.log('- testPreviewUpdate(): 测试预览更新');
console.log('- simulateConfigChange(): 模拟配置变化');
console.log('');
console.log('使用方法:');
console.log('1. 选择知识点并打开组卷模态框');
console.log('2. 切换到"试卷预览"选项卡');
console.log('3. 在控制台运行 checkAndFixPreview() 检查状态');
console.log('4. 运行 testPreviewRequestFormat() 验证请求数据格式');
console.log('5. 如果有问题，运行 forceInitializePreview() 强制修复');
console.log('6. 最后运行 runRealTimePreviewTests() 进行完整测试');
